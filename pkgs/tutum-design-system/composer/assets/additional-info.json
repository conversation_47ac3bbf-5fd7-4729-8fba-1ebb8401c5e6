[{"fK": "5002", "label": "Type of treatment", "minLength": 0, "maxLength": 60, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5003", "label": "(N)BSNR of the mediated specialist doctor", "minLength": 9, "maxLength": 9, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5005", "label": "Multiplicator", "minLength": 3, "maxLength": 3, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5006", "label": "Time", "minLength": 4, "maxLength": 4, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["TIMEHHMM"]}, {"treatmentCase": "0102", "ruleNames": ["TIMEHHMM"]}, {"treatmentCase": "0103", "ruleNames": ["TIMEHHMM"]}, {"treatmentCase": "0104", "ruleNames": ["TIMEHHMM"]}]}, {"fK": "5008", "label": "DKM", "minLength": 0, "maxLength": 3, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5302", "label": "LANR/BSNR of referral Doctor", "minLength": null, "maxLength": null, "isMany": true, "dataType": "ReferralBlock", "inputType": "ReferralBlock", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["700W"]}, {"treatmentCase": "0102", "ruleNames": ["700W"]}, {"treatmentCase": "0103", "ruleNames": ["700W"]}, {"treatmentCase": "0104", "ruleNames": ["700W"]}]}, {"fK": "5010", "label": "Charge number", "minLength": 0, "maxLength": 60, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["868W"]}, {"treatmentCase": "0102", "ruleNames": ["868W"]}, {"treatmentCase": "0103", "ruleNames": ["868W"]}, {"treatmentCase": "0104", "ruleNames": ["868W"]}]}, {"fK": "5011", "label": "Material costs", "minLength": null, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "DropDownListApi", "dataSource": "material cost", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}], "additionalInformations": [{"fK": "5012", "label": "Costs in cent", "minLength": null, "maxLength": null, "isMany": true, "dataType": "int", "inputType": "NumberInput", "dataSource": "material cost", "isRequired": true, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["710W"]}, {"treatmentCase": "0102", "ruleNames": ["710W"]}, {"treatmentCase": "0103", "ruleNames": ["710W"]}, {"treatmentCase": "0104", "ruleNames": ["710W"]}]}, {"fK": "5074", "label": "Name producer / supplier", "minLength": null, "maxLength": null, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "material cost", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5075", "label": "Article- /Modellnumber", "minLength": 0, "maxLength": 60, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5076", "label": "Rechnungsnummer", "minLength": 0, "maxLength": 20, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}]}, {"fK": "5013", "label": "Percent of the treatment", "minLength": 3, "maxLength": 3, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5015", "label": "Organ", "minLength": 0, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5016", "label": "Doctor name", "minLength": 0, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5017", "label": "Place of visit for home visits", "minLength": 0, "maxLength": 60, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0104"}]}, {"fK": "5018", "label": "Zone of visits", "minLength": null, "maxLength": null, "isMany": false, "dataType": "string", "inputType": "DropDownList", "dataSource": "zone of visits", "dataSourceValues": ["Z1", "Z2", "Z3", "Z4"], "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["111F"]}, {"treatmentCase": "0102", "ruleNames": ["111F"]}, {"treatmentCase": "0103", "ruleNames": ["111F"]}, {"treatmentCase": "0104", "ruleNames": ["111F"]}]}, {"fK": "5019", "label": "Location of treatment", "minLength": 0, "maxLength": 60, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5020", "label": "Repet of treatment", "minLength": null, "maxLength": null, "isMany": false, "dataType": "int", "inputType": "DropDownList", "dataSource": "repet of treatment", "dataSourceValues": ["1", "0"], "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["147F"]}, {"treatmentCase": "0102", "ruleNames": ["147F"]}, {"treatmentCase": "0103", "ruleNames": ["147F"]}, {"treatmentCase": "0104", "ruleNames": ["147F"]}], "additionalInformations": [{"fK": "5021", "label": "Year of the last cancer early detection treatment", "minLength": 4, "maxLength": 4, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}]}, {"fK": "5023", "label": "Go Number addition", "minLength": 1, "maxLength": 1, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5024", "label": "GNR- additional participation for post-inpatient services rendered", "minLength": 1, "maxLength": 1, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5025", "label": "Recording date", "minLength": 8, "maxLength": 8, "isMany": false, "dataType": "DateEpoc", "inputType": "DateInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0102", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0103", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0104", "ruleNames": ["DDMMYYYY"]}]}, {"fK": "5026", "label": "Date of discharge", "minLength": 8, "maxLength": 8, "isMany": false, "dataType": "DateEpoc", "inputType": "DateInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0102", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0103", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0104", "ruleNames": ["DDMMYYYY"]}]}, {"fK": "5034", "label": "OP date", "minLength": 8, "maxLength": 8, "isMany": false, "dataType": "DateEpoc", "inputType": "DateInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0102", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0103", "ruleNames": ["DDMMYYYY"]}, {"treatmentCase": "0104", "ruleNames": ["DDMMYYYY"]}]}, {"fK": "5035", "label": "OP key", "minLength": 0, "maxLength": 8, "isMany": true, "dataType": "string", "inputType": "DropDownListApi", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["702W", "703W"]}, {"treatmentCase": "0102", "ruleNames": ["702W", "703W"]}, {"treatmentCase": "0103", "ruleNames": ["702W", "703W"]}, {"treatmentCase": "0104", "ruleNames": ["702W", "703W"]}], "additionalInformations": [{"fK": "5041", "label": "Page localization OPS", "minLength": null, "maxLength": null, "isMany": false, "dataType": "string", "inputType": "DropDownList", "dataSource": "ops", "dataSourceValues": ["R", "L", "B"], "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["705W"]}, {"treatmentCase": "0102", "ruleNames": ["705W"]}, {"treatmentCase": "0103", "ruleNames": ["705W"]}, {"treatmentCase": "0104", "ruleNames": ["705W"]}]}]}, {"fK": "5036", "label": "GNR as Condition", "minLength": 5, "maxLength": 6, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["702W", "704W"]}, {"treatmentCase": "0102", "ruleNames": ["702W", "704W"]}, {"treatmentCase": "0103", "ruleNames": ["702W", "704W"]}, {"treatmentCase": "0104", "ruleNames": ["702W", "704W"]}]}, {"fK": "5038", "label": "Complication", "minLength": 0, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5040", "label": "Patientnumer (EDV) large sheet of FEK or eDocumentation of skin cancer screening", "minLength": 0, "maxLength": 8, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0103"}, {"treatmentCase": "0104"}]}, {"fK": "5042", "label": "Quantity specification KM / AM", "minLength": 0, "maxLength": 5, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["707W"]}, {"treatmentCase": "0102", "ruleNames": ["707W"]}, {"treatmentCase": "0103", "ruleNames": ["707W"]}, {"treatmentCase": "0104", "ruleNames": ["707W"]}], "additionalInformations": [{"fK": "5043", "label": "Unit of measure KM / AM", "minLength": null, "maxLength": null, "isMany": false, "dataType": "int", "inputType": "DropDownList", "dataSource": "unit of measure km / am", "dataSourceValues": ["1", "2", "3"], "isRequired": true, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["106F"]}, {"treatmentCase": "0102", "ruleNames": ["106F"]}, {"treatmentCase": "0103", "ruleNames": ["106F"]}, {"treatmentCase": "0104", "ruleNames": ["106F"]}]}]}, {"fK": "5050", "label": "Melde-ID Implantateregister", "minLength": 10, "maxLength": 10, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["888W"]}, {"treatmentCase": "0102", "ruleNames": ["888W"]}, {"treatmentCase": "0103", "ruleNames": ["888W"]}, {"treatmentCase": "0104", "ruleNames": ["888W"]}], "additionalInformations": [{"fK": "5051", "label": "Hash-String Implantateregister", "minLength": 0, "maxLength": 512, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["889W"]}, {"treatmentCase": "0102", "ruleNames": ["889W"]}, {"treatmentCase": "0103", "ruleNames": ["889W"]}, {"treatmentCase": "0104", "ruleNames": ["889W"]}]}, {"fK": "5052", "label": "Hash-<PERSON><PERSON>ateregister", "minLength": 64, "maxLength": 64, "isMany": false, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["890W"]}, {"treatmentCase": "0102", "ruleNames": ["890W"]}, {"treatmentCase": "0103", "ruleNames": ["890W"]}, {"treatmentCase": "0104"}]}]}, {"fK": "5070", "label": "OMIM-G-Code of the investigated Gens", "minLength": null, "maxLength": null, "isMany": true, "dataType": "int", "inputType": "DropDownListApi", "dataSource": "omim-p-code.", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}, {"treatmentCase": "0102", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}, {"treatmentCase": "0103", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}, {"treatmentCase": "0104", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}], "additionalInformations": [{"fK": "5072", "label": "Gen-Name", "minLength": 0, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["772F"]}, {"treatmentCase": "0102", "ruleNames": ["772F"]}, {"treatmentCase": "0103", "ruleNames": ["772F"]}, {"treatmentCase": "0104", "ruleNames": ["772F"]}]}]}, {"fK": "5071", "label": "OMIM-P-Code (Type of disease)", "minLength": null, "maxLength": null, "isMany": true, "dataType": "int", "inputType": "DropDownListApi", "dataSource": "omim-p-code.", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["770F", "816F", "828F", "834F", "843F", "847I", "854F"]}, {"treatmentCase": "0102", "ruleNames": ["770F", "816F", "828F", "834F", "843F", "847I", "854F"]}, {"treatmentCase": "0103", "ruleNames": ["770F", "816F", "828F", "834F", "843F", "847I", "854F"]}, {"treatmentCase": "0104", "ruleNames": ["770F", "816F", "828F", "834F", "843F", "847I", "854F"]}], "additionalInformations": [{"fK": "5073", "label": "Type of disease", "minLength": 0, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["773F"]}, {"treatmentCase": "0102", "ruleNames": ["773F"]}, {"treatmentCase": "0103", "ruleNames": ["773F"]}, {"treatmentCase": "0104", "ruleNames": ["773F"]}]}]}, {"fK": "5098", "label": "(N)BSNR of the place of service provision", "minLength": 9, "maxLength": 9, "isMany": false, "dataType": "string", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["823FORMAT"]}, {"treatmentCase": "0102", "ruleNames": ["823FORMAT"]}, {"treatmentCase": "0103"}, {"treatmentCase": "0104", "ruleNames": ["823FORMAT"]}, {"treatmentCase": "0109"}, {"treatmentCase": "sad1"}, {"treatmentCase": "sad2"}, {"treatmentCase": "sad3"}]}, {"fK": "5099", "label": "Lifelong doctor number of the contract doctor / contract psychotherapist", "minLength": 9, "maxLength": 9, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["725FORMAT", "837F", "762F"]}, {"treatmentCase": "0102", "ruleNames": ["725FORMAT", "837F", "762F"]}, {"treatmentCase": "0103", "ruleNames": ["725FORMAT", "837F", "762F"]}, {"treatmentCase": "0104", "ruleNames": ["725FORMAT", "837F", "762F"]}, {"treatmentCase": "0109"}, {"treatmentCase": "sad1"}, {"treatmentCase": "sad2"}, {"treatmentCase": "sad3"}]}, {"fK": "5102", "label": "Hospital ID (as part of ASV billing)", "minLength": 9, "maxLength": 9, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0104"}]}, {"fK": "5101", "label": "Pseudo-LANR of the doctor (for hospital doctors as part of ASV billing)", "minLength": 9, "maxLength": 9, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["837F"]}, {"treatmentCase": "0102", "ruleNames": ["837F"]}, {"treatmentCase": "0104", "ruleNames": ["837F"]}]}, {"fK": "5100", "label": "ASV team number of the contract doctor", "minLength": 9, "maxLength": 9, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0102"}]}, {"fK": "5200", "label": "Pseudo-GNR", "minLength": 5, "maxLength": 6, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101"}, {"treatmentCase": "0102"}, {"treatmentCase": "0104"}]}, {"fK": "5900", "label": "Day separation", "minLength": 4, "maxLength": 4, "isMany": false, "dataType": "int", "inputType": "NumberInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["TIMEHHMM"]}, {"treatmentCase": "0102", "ruleNames": ["TIMEHHMM"]}, {"treatmentCase": "0103", "ruleNames": ["TIMEHHMM"]}, {"treatmentCase": "0104", "ruleNames": ["TIMEHHMM"]}]}, {"fK": "5300", "label": "Total Amount", "minLength": 0, "maxLength": null, "isMany": false, "dataType": "int", "inputType": "DecimalInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": null}, {"fK": "5009", "label": "Free Condition Text", "minLength": 0, "maxLength": null, "isMany": false, "dataType": "string", "inputType": "DropDownCreateList", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["700W"]}, {"treatmentCase": "0102", "ruleNames": ["700W"]}, {"treatmentCase": "0103", "ruleNames": ["700W"]}, {"treatmentCase": "0104", "ruleNames": ["700W"]}]}, {"fK": "5077", "label": "HGNC-Gensymbol", "minLength": null, "maxLength": null, "isMany": true, "dataType": "int", "inputType": "DropDownListApi", "dataSource": "hgnc-gensymbol-code.", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}, {"treatmentCase": "0102", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}, {"treatmentCase": "0103", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}, {"treatmentCase": "0104", "ruleNames": ["770F", "816F", "828F", "829F", "830F", "834F", "843F", "854F"]}], "additionalInformations": [{"fK": "5078", "label": "Gen-Name", "minLength": 0, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["772F"]}, {"treatmentCase": "0102", "ruleNames": ["772F"]}, {"treatmentCase": "0103", "ruleNames": ["772F"]}, {"treatmentCase": "0104", "ruleNames": ["772F"]}]}]}, {"fK": "5079", "label": "Type of illness", "minLength": 0, "maxLength": null, "isMany": true, "dataType": "string", "inputType": "TextInput", "dataSource": "", "isRequired": false, "treatmentCaseWithRule": [{"treatmentCase": "0101", "ruleNames": ["773F"]}, {"treatmentCase": "0102", "ruleNames": ["773F"]}, {"treatmentCase": "0103", "ruleNames": ["773F"]}, {"treatmentCase": "0104", "ruleNames": ["773F"]}]}]