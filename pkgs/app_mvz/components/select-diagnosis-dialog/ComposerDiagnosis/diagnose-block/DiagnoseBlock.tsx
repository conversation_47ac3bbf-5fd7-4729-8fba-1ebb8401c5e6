import React, { useState, useContext, useEffect, useCallback } from 'react';
import { Flex, IMenuItemWithData, Svg } from '@tutum/design-system/components';
import I18n from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import type CommonI18n from '@tutum/mvz/locales/en/Common.json';
import {
  Certainty,
  Laterality,
} from '@tutum/hermes/bff/service_domains_patient_file';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import type { IDiagnoseSearchResult } from './DiagnoseBlock.type';
import ScheinBlock from '@tutum/mvz/components/schein-block';
import { parseToScheinMainGroup } from '../helpers';
import DiagnoseBlockService from './DiagnoseBlock.service';
import SdvaDetailsDialog from '@tutum/mvz/components/sdva-dialog';
import DiagnosisNodeComponent from '@tutum/design-system/composer/diagnosis-node-component';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import debounce from 'lodash/debounce';
import { EncounterDiagnoseTimeline } from '@tutum/hermes/bff/repo_encounter';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import { useQueryGetSetting } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  removeDiagnoseCodeFromText,
  renderDiagnoseLabel,
} from '@tutum/mvz/module_patient-management/patient-file/timeline/timeline-content/timeline-entry/diagnose-entry/helpers';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  groupScheinItemAndInsurance,
  groupQuarterbySchein,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.helper';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { useActionBarStore } from '@tutum/mvz/module_patient-management/patient-file/encounter-v2/action-bar/ActionBar.store';

export interface IDiagnoseBlockProps {
  initEncounterId: string;
  disabled?: boolean;
  data?: EncounterDiagnoseTimeline;
  scheinsFilteredByDate: ScheinItem[];
  onChange(data: EncounterDiagnoseTimeline): Promise<void>;
  onSubmit: () => Promise<void>;
  onClear: () => void;
  command?: string;
  technicalSchein?: Nullable<ScheinItem>;
  isShowScheinBlock?: boolean;
}

const DiagnoseBlock: React.FC<IDiagnoseBlockProps> = (props) => {
  const {
    disabled,
    data,
    scheinsFilteredByDate,
    onChange,
    onSubmit,
    onClear,
    command,
    technicalSchein,
    isShowScheinBlock,
  } = props;

  const { t } = I18n.useTranslation<
    keyof typeof PatientManagementI18n.Composer
  >({
    namespace: 'PatientManagement',
    nestedTrans: 'Composer',
  });

  const { t: tCommon } = I18n.useTranslation<keyof typeof CommonI18n.HintError>(
    {
      namespace: 'Common',
      nestedTrans: 'HintError',
    }
  );

  const {
    patientManagement: { patient },
  } = useContext(PatientManagementContext.instance);

  const patientFileStore = usePatientFileStore();
  const { encounterDate } = useActionBarStore();
  const setting = useSettingStore();
  const [isSdvaDetailsOpen, setIsSdvaDetailsOpen] = useState(false);

  const [selectedSchein, setSelectedSchein] = useState<ScheinItem | undefined>(
    undefined
  );

  const handleSearchDiagnose = async (
    keyWord: string,
    cb?: (result: IDiagnoseSearchResult[]) => void
  ) => {
    if (!patient) {
      return;
    }

    const result = await DiagnoseBlockService.searchDiagnose(
      patient,
      +datetimeUtil.date(),
      keyWord,
      tCommon,
      command
    );
    cb?.(result);
  };

  const onICDChange = async (diagnoseItem: EncounterDiagnoseTimeline) => {
    if (!patientFileStore.schein.activatedSchein || !data) {
      return;
    }

    const updatedDiagnose: EncounterDiagnoseTimeline = {
      ...data,
      code: diagnoseItem.code,
      description: diagnoseItem.description,
      freeText: `${diagnoseItem.code ? `(${diagnoseItem.code}) ` : ''}${
        diagnoseItem.description
      }`,
      group: diagnoseItem.group,
      scheins: data?.scheins?.length
        ? data.scheins
        : parseToScheinMainGroup([patientFileStore.schein.activatedSchein]),
      certainty:
        DiagnoseBlockService.getDefaultCertainty(diagnoseItem.code) ??
        data?.certainty,
      sdvaRefs: diagnoseItem.sdvaRefs,
    };
    await onChange(updatedDiagnose);
  };

  const onCertaintyChange = async (_data: Certainty) => {
    if (data?.certainty === _data || !data) {
      return;
    }

    await onChange({
      ...data,
      certainty: _data,
    });
  };

  const onLateralityChange = async (_data: Laterality) => {
    if (data?.laterality === _data || !data) {
      return;
    }

    await onChange({
      ...data,
      laterality: _data,
    });
  };

  const onCloseSdvaDialog = () => setIsSdvaDetailsOpen(!isSdvaDetailsOpen);

  const onSdvaInfoClick = () => {
    setIsSdvaDetailsOpen(true);
  };

  const NoResults = () => {
    return <>{t('noResultsFound')}</>;
  };

  const onSetScheinIds = useCallback(
    (scheinItems: ScheinItem[]) => {
      if (!data) {
        return;
      }
      const items = parseToScheinMainGroup(scheinItems);
      setSelectedSchein(scheinItems[0]);
      onChange({
        ...data,
        scheins: items,
      });
    },
    [onChange, data, scheinsFilteredByDate]
  );

  const createListInsuranceMapBySchein = () => {
    if (!patientFileStore.patient.current) {
      return [];
    }
    const listInsurance =
      patientFileStore.patient.current.patientInfo.insuranceInfos;

    const listScheinId = scheinsFilteredByDate.map(
      (schein) => schein.insuranceId
    );
    const res = listInsurance.filter((insur) =>
      listScheinId.includes(insur.id)
    );
    const groupQuarterBySchein = groupQuarterbySchein(
      scheinsFilteredByDate,
      res
    );
    if (
      technicalSchein &&
      technicalSchein.g4101Year &&
      technicalSchein.g4101Quarter
    ) {
      groupQuarterBySchein.push({
        groupKey: {
          year: technicalSchein.g4101Year,
          quarter: technicalSchein.g4101Quarter,
        },
        groupValue: groupScheinItemAndInsurance(
          technicalSchein,
          [technicalSchein],
          res
        ),
      });
    }
    return groupQuarterBySchein;
  };

  const { data: settingData, isLoading } = useQueryGetSetting({
    select: (data) => data.data,
  });

  useEffect(() => {
    if (!data) {
      return;
    }
    if (technicalSchein) {
      setSelectedSchein(technicalSchein);
      onChange({
        ...data,
        scheins: technicalSchein
          ? parseToScheinMainGroup([technicalSchein])
          : [],
      });
    } else {
      const scheinValid =
        scheinsFilteredByDate.find(
          (schein) =>
            schein.scheinId ===
            patientFileStore?.schein?.activatedSchein?.scheinId
        ) || scheinsFilteredByDate?.length === 1
          ? scheinsFilteredByDate[0]
          : ({} as ScheinItem);

      setSelectedSchein((scheinValid || {}) as ScheinItem);
      onChange({
        ...data,
        scheins: scheinValid ? parseToScheinMainGroup([scheinValid]) : [],
      });
    }
  }, [patientFileStore, scheinsFilteredByDate, technicalSchein]);

  const isDisplayInfoIcon =
    data?.freeText &&
    data?.freeText.length > 2 &&
    data?.sdvaRefs != undefined &&
    data?.sdvaRefs.length > 0;

  const shouldShowScheinBlock = isShowScheinBlock && Boolean(data?.code);

  const icdDefaultValue:
    | IMenuItemWithData<EncounterDiagnoseTimeline>
    | undefined = data?.description
    ? {
        id: '',
        label: `${data?.code != '' ? `(${data?.code}) ` : ''}${
          data?.description
        }`,
        value: data?.code,
        data: data,
      }
    : undefined;

  return (
    <Flex align="flex-start" auto>
      {isDisplayInfoIcon && (
        <Flex gap={4} pt={2} pr={4}>
          <Svg
            className="info-icon"
            src="/images/icon-info.svg"
            onClick={() => onSdvaInfoClick()}
          />
          <SdvaDetailsDialog
            isOpen={isSdvaDetailsOpen}
            onClose={onCloseSdvaDialog}
            icdCode={data?.code}
            sdvaRefs={data?.sdvaRefs}
            encounterDate={encounterDate || datetimeUtil.now()}
            t={t}
          />
        </Flex>
      )}
      {!isDisplayInfoIcon && <div className="padder"></div>}
      <Flex auto>
        <DiagnosisNodeComponent
          autoFocus
          onClear={onClear}
          onSubmit={onSubmit}
          disabled={disabled}
          placeholder={t('typeDiagnose')}
          defaultIcdValue={icdDefaultValue}
          formatIcdLabel={(opt) => renderDiagnoseLabel(opt.data)}
          actionBarValues={{} as any}
          onIcdCodeSelect={(item) =>
            onICDChange(item?.data as EncounterDiagnoseTimeline)
          }
          onSearchIcdList={async (query: string, setICDOptions) => {
            const debouncedFn = debounce(async (q: string, setOpts) => {
              if (!data || q === '') {
                return;
              }
              await onICDChange({
                ...data,
                description: removeDiagnoseCodeFromText(q, data?.code),
              });
              await handleSearchDiagnose(q, (results) =>
                setOpts(
                  results.map((rs) => ({
                    label: renderDiagnoseLabel(rs),
                    value: rs.code,
                    data: rs,
                  }))
                )
              );
            }, 100);
            return debouncedFn(query, setICDOptions);
          }}
          certaintyLabel={t('certainty')}
          onCertaintyChange={(item: Certainty) => onCertaintyChange(item)}
          defaultCertaintyValue={data?.certainty}
          lateralityLabel={t('laterality')}
          onLateralityChange={(item: Laterality) => onLateralityChange(item)}
          defaultLateralityValue={data?.laterality}
          renderNoResults={<NoResults />}
          freeText={data?.freeText || ''}
          scaleNumber={setting?.timelineSetting?.scaleNumber}
        />
      </Flex>
      {shouldShowScheinBlock && (
        <ScheinBlock
          selectedSchein={selectedSchein}
          scheinFilter={createListInsuranceMapBySchein()}
          onSetScheinIds={onSetScheinIds}
          openCreateSchein={() => {}}
        />
      )}
    </Flex>
  );
};

export default React.memo(DiagnoseBlock);
