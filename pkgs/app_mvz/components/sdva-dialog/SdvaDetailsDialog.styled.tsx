import React from 'react';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { mixTypography, scaleSpacePx } from '@tutum/design-system/styles';
import Theme from '@tutum/mvz/theme';
import OriginalSdvaDetailsDialog, {
  SdvaDetailsDialogProps,
} from './SdvaDetailsDialog';

const styled = Theme.styled;
const SdvaDetailsDialog: React.ComponentType<SdvaDetailsDialogProps> = styled(
  OriginalSdvaDetailsDialog
).attrs(({ className }) => ({
  className: getCssClass('sl-sdva-details-dialog', className),
}))`
  & {
    .dialog-content {
      padding: ${scaleSpacePx(6)};
      gap: ${scaleSpacePx(6)};
    }

    h4 {
      ${mixTypography('h4')}
    }

    div,
    p {
      ${mixTypography('bodyS')}
      margin-bottom: ${scaleSpacePx(2)}
    }

    table {
      margin: ${scaleSpacePx(1)} 0;
      border-collapse: collapse;
      td,
      th {
        border: 1px solid lightgray;
      }
      td {
        padding: 2px ${scaleSpacePx(2)};
        min-width: 40px;
      }
    }
  }
`;

export default SdvaDetailsDialog;
