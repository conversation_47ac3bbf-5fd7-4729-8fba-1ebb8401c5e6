import React from 'react';

import { Classes, Dialog } from '@tutum/design-system/components/Core';
import { BodyTextL, Flex } from '@tutum/design-system/components';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';
import { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import type PatientManagementI18n from '@tutum/mvz/locales/en/PatientManagement.json';
import { Chapter } from '@tutum/hermes/bff/legacy/sdva_common';
import { useQuerySearchSdva } from '@tutum/hermes/bff/legacy/app_mvz_patient_encounter';

export type SdvaDetailsDialogProps = {
  className?: string;
  isOpen: boolean;
  onClose: () => void;
  icdCode?: string;
  sdvaRefs?: string[];
  encounterDate: number;
  t: IFixedNamespaceTFunction<keyof typeof PatientManagementI18n.Composer>;
};

const SdvaDetailsDialog = ({
  className,
  isOpen,
  onClose,
  icdCode,
  sdvaRefs,
  encounterDate,
  t,
}: SdvaDetailsDialogProps) => {
  const { data: sdvaData, isSuccess } = useQuerySearchSdva(
    {
      query: sdvaRefs || [],
      selectedDate: encounterDate,
    },
    {
      enabled: isOpen && sdvaRefs && sdvaRefs.length > 0 && encounterDate > 0,
    }
  );

  const renderChapter = (c: Chapter) => {
    return (
      <Flex column key={c.key}>
        <BodyTextL fontWeight="Bold">{`${c.order} ${c.title}`}</BodyTextL>
        <div dangerouslySetInnerHTML={{ __html: c.description || '' }}></div>
      </Flex>
    );
  };

  return (
    <div>
      {sdvaRefs && isSuccess && sdvaData?.items?.length > 0 ? (
        <Dialog
          className={getCssClass(
            'bp5-dialog-halfscreen',
            'bp5-dialog-content-scrollable',
            className
          )}
          isOpen={isOpen}
          title={`${t('EncryptionInstructions')}: ${icdCode}`}
          onClose={onClose}
          canOutsideClickClose={false}
        >
          <Flex className={`${Classes.DIALOG_BODY} dialog-content`} column>
            {sdvaData.items.map((r) => renderChapter(r))}
          </Flex>
        </Dialog>
      ) : (
        <></>
      )}
    </div>
  );
};

export default SdvaDetailsDialog;
