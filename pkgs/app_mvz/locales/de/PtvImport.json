{"title": "PTV Import", "PtvImportListContract": {"ptvImportContractHeader": "Neue Datei importieren", "ptvImportContractDescriptionRemark": "<PERSON><PERSON><PERSON><PERSON>", "ptvImportContractDescriptionDetail1": "Sie können die Funktion zum Import der Patiententeilnahmeinformationen aus Ihren Informationsbriefen Patiententeilnahmestatus (kurz: PTV-Import) mithilfe des ICodes erst nutzen, wenn Sie erfolgreich im Arztportal registriert sind und dort Ihre vertraulichen Dokumente (d.h. Abrechnungsnachweis und Informationsbrief Patiententeilnahmestatus) herunterladen.\nSollten Sie noch nicht im Arztportal registriert sein, so holen dies jetzt unter ", "ptvImportContractDescriptionDetailLink": "arztportal.net", "ptvImportContractDescriptionDetail2": "nach und nutzen Sie ab sofort die Vorteile des PTV-Importes.", "step1": "Importieren für", "doctor": "Arz<PERSON>", "doctorPlaceHolder": "Arzt auswählen", "icode": "ICode", "verifyDoctor": "Abrufen", "upload": "Upload", "uploadSuccess": "Import wurde durchgeführt", "uploadFailed": "Der Import konnte nicht durchgeführt werden. Versuchen Sie es erneut.", "dataRequested": "Daten werden angefragt", "dataRequestFailed": "Daten konnten nicht abgerufen werden, bitte versuchen Si<PERSON> es erneut.", "iCodeOutDated": "ICode ist veraltet, bitte verwenden Sie einen gültigen ICode für das aktuelle Quartal.", "step2": "Auswählen", "descriptionOfListContract": "Bitte wähle für den Import eine PTV Datei aus", "contractId": "Vertrag", "quarterTime": "Quartal", "versionDocument": "version", "status": "garrioPRO Status", "tooltipImported": "Erfolgreich importiert", "importBtn": "Importieren", "importedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notYetImportLabel": "<PERSON>eu", "noResultFound": "<PERSON><PERSON> vorhanden", "ConfirmDialog": {"title": "PTV erneut importieren?", "description": "Das ausgewählte Verzeichnis wurde bereits importiert. Sind Si<PERSON> sicher, dass dieses erneut importiert und überschrieben werden soll?", "cancelBtn": "<PERSON><PERSON>", "confirmBtn": "Ja, importieren"}}, "PtvImportConfirmImportContract": {"titleWarning": "<PERSON><PERSON><PERSON>(en)", "descriptionWarning": "wurde bereits schon importiert. Möchtest du die zuvor importierte Datei ersetzen?", "yesWarning": "<PERSON>a", "noWarning": "<PERSON><PERSON>"}, "PtvImportList": {"downloaded": "Download PDF", "import": "Import", "contractId": "Vertrag", "doctorName": "Arz<PERSON>", "quarterTime": "Zeitraum", "status": "garrioPRO Status", "importerName": "Import<PERSON><PERSON> durch", "importerTime": "Importiert am", "importNew": "<PERSON>eu", "importSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importInProgress": "Daten werden importiert", "importPending": "<PERSON><PERSON><PERSON><PERSON>", "patientTotal": "Patient", "importProtocol": "Import-Protokoll", "cantImport": "<PERSON>s können keine veralteten PTV importiert werden", "testRunReport": "Prüflaufbericht", "noResultFound": "<PERSON>s wurden noch keine Daten importiert"}, "PtvImportProtocol": {"importProtocolTitle": "Import-Protokoll", "contractId": "Vertrag", "doctor": "Arz<PERSON>", "doctorInfo": "{{doctorName}} (LANR: {{doctor<PERSON>anr}}, HAVG VP ID: {{doctorHavgVpId}})", "quarterTime": "Quartal", "importInfo": "Import<PERSON><PERSON> durch", "importDate": "Startdatum Import", "importDateFormat": "{{date}} at {{time}}", "totalAmounted": "G<PERSON>am<PERSON>zahl:", "patients": "{{total}} <PERSON><PERSON><PERSON>", "updateAll": "Alle aktualisieren", "autoImportTag": "Normale Teilnahmemeldungen", "total": "Gesamt", "noResultFound": "<PERSON><PERSON>", "firstName": "vorname", "firstNamePtv": "<PERSON><PERSON><PERSON>", "firstNameIv": "<PERSON><PERSON><PERSON>", "lastName": "nachname", "lastNamePtv": "Nachname", "lastNameIv": "Nachname", "dob": "geburtsdatum", "dobPtv": "Geburtsdatum", "dobIv": "Geburtsdatum", "insuranceNumber": "Versicherungsnummer", "insuranceNumberPtv": "Versicherungsnummer", "insuranceNumberIv": "Versicherungsnummer", "status": "garrioPRO Status", "statusPtv": "PTV Status", "statusIv": "Status", "beginDateContract": "Beginndatum", "beginDateContractPtv": "Beginndatum", "beginDateContractIv": "Beginndatum", "endDateContract": "Enddatum", "endDateContractPtv": "Enddatum", "endDateContractIv": "Enddatum", "requestDate": "Beantragt am", "rejectDate": "Abgelehnt am", "ikNumber": "Kassen-IK", "ikNumberPtv": "IK", "ikNumberIv": "IK", "reason": "begründung", "note": "Note", "hint": "<PERSON><PERSON><PERSON><PERSON>", "conflictImportTag": "Besondere Teilnahmemeldungen", "conflictDescription": "Folgende HZV-Teilnehmer konnten nicht automatisch in Ihrer Praxissoftware aktualisiert werden! Bitte prüfen Sie diese Fälle und führen Sie die Aktualisierungen der HZV-Teilnahmen manuell durch. Verwenden Sie stets den aktuellen Infobrief, um eine korrekte Abrechnung sicherzustellen. Wenn Si<PERSON> hierzu Rückfragen haben, steht Ihnen unter der Telefonnummer 02203/5756-1111 der HÄVG-Kundenservice gerne zur Verfügung.", "conflictFooter": "Unterschiedliche Werte in der Praxissoftware und in der Patiententeilnehmerverzeichnis (PTV) für {{quarterTime}} werden fett dargestellt", "resolveConflictBtn": "Konflikte beheben", "conflictResolved": "Überprüft", "actions": "Aktionen", "keepExistVersion": "bestehende Patienteninformation behalten", "updateTo": "Aktualisieren zu", "saveBtn": "Speichern", "missingImportTag": "Nicht gefundene Patienten", "importAll": "Alle importieren", "assignPatient": "<PERSON><PERSON>", "assignTo": "Assigned to {{patientName}}", "assignPatientDialog": {"title": "Importierte Daten aktualisieren", "selectPatient": "<PERSON><PERSON> Ausw<PERSON>hl<PERSON>", "selectPatientRequired": "<PERSON><PERSON> ist ein Pflich<PERSON>feld", "noResultFound": "<PERSON>s wurden keine Ergebnisse gefunden", "createNewPatient": "Neuen Patient erstellen", "patientAssigned": "Patient wurde zugewiesen", "dob": "Geburtsdatum:", "insuranceNumber": "Versichertennummer:", "insuranceInfo": "Versicherung:", "ik": "IK"}, "proccessing": "PTV wird importiert", "missingDescription": "„Nicht gefundene Patienten können beim Import nicht eindeutig identifiziert werden. Wurde ein Patient nicht eindeutig identifiziert, so stimmen in der Regel die Versichertennummer und das Geburtsdatum eines Patienten in Ihrer Praxissoftware und im Patiententeilnehmerverzeichnis nicht überein oder die Teilnahmeanfrage ist noch ausstehend. Die Teilnahmeinformationen für nicht gefundene Patienten müssen deshalb manuell bearbeitet werden.“", "missingPtvDescription": "Folgende Patienten ({{participantNo}}) sind in der Praxissoftware: enthalten, konnten aber nicht in der Teilnehmerdatei gefunden werden", "downloadPdf": "PDF herunterladen", "zeroPatient": "<PERSON><PERSON>"}, "PtvImportDryRun": {"title": "Prüflauf-Bericht", "titleBasicImport": "Import-Protokoll", "titleFullImport": "Vollimport", "contractId": "Vertrag", "doctor": "Arzt:", "doctorInfo": "{{doctorName}} (LANR: {{doctor<PERSON>anr}}, HAVG VP ID: {{doctorHavgVpId}})", "quarterTime": "Quartal:", "importInfo": "Importiert durch:", "importDate": "Startdatum Import:", "titleNormalList": "A. Normale Teilnahmemeldungen", "description": "Beschreibung", "numberPatient": "<PERSON><PERSON><PERSON>", "unchangedCase": "Unveränderte Teilnehmer", "newCase": "Neu gemeldete Teilnahmen", "terminatedCase": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "requestedCase": "In Prüfung befindliche Patienten", "rejectedCase": "Neu abgelehnte Patienten", "titleSpecialList": "B. Besondere Teilnahmemeldungen", "specialCase": "<PERSON><PERSON>hl besonderer Teilnahmemeldungen", "titleNotFoundList": "C. Nicht gefundene Patienten", "titleSectionD": "D. Prognose Teilnehmeranzahl vor Import / nach Import der normalen Teilnahmemeldungen", "additionalInfo1": "Besondere Teilnahmemeldungen sind z.B. Stornierungen, rückwirkende Einschreibungen, rückwirkende Beendigungen.", "additionalInfo2": "Die Daten für besondere Teilnahmemeldungen und nicht gefundene Patienten werden beim Standardimport nicht automatisch in die Praxissoftware übernommen. Sie können nach dem Prüflauf wähl<PERSON>, einen Vollimport durchzuführen, um die Teilnahmedaten für besondere Teilnahmemeldungen sowie eindeutig identifizierte nicht gefundene Patienten automatisch in die Praxissoftware zu übernehmen. Andernfalls müssen Sie die Daten für diese Patienten nach dem Import überprüfen und ggf. manuell in die Praxissoftware übernehmen. Die Daten für besondere Teilnahmemeldungen und nicht gefundene Patienten werden nach dem Standardimport detailliert im Import-Protokoll aufgelistet.", "additionalInfo3": "Nicht gefundene Patienten können beim Import nicht generell eindeutig identifiziert werden (Stammdaten) und daher auch nicht generell automatisch übernommen werden. Mit dem Vollimport können manuelle Nacharbeiten reduziert werden", "missingInHPM": "In der Praxissoftware vorhandene Patienten, mit dem Teilnahmestatus beantragt oder aktiviert, die nicht im Patiententeilnehmerverzeichnis gefunden wurden.", "missingInPractice": "Im Patiententeilnehmerverzeichnis gemeldete Patienten, die nicht in der Praxissoftware gefunden wurden.", "beforeImport": "Vor Import", "afterImport": "Nach Import", "downloadPdf": "PDF herunterladen", "import": "Importieren", "backStep": "Zurück", "nextStep": "<PERSON><PERSON>", "updateAll": "Alle aktualisieren", "ignoreConflictsAndUpdateAll": "Konflikte ignorieren und alle aktualisieren", "confirmImportDialog": {"title": "PTV Daten importieren?", "description": "Sind <PERSON> sic<PERSON>, dass Sie PTV-Daten importieren und den Status der Patienten entsprechend zu aktualisieren?", "cancelBtn": "Abbrechen", "confirmBtn": "Ja, importieren", "titleIgnoreConflict": "Konflikte ignorieren?", "descriptionIgnoreConflict": "Sind <PERSON> sicher, dass Sie alle Patienten mit ungelösten Konflikten ignorieren und alle aktualisieren?", "cancelBtnIgnoreConflict": "Abbrechen", "confirmBtnIgnoreConflict": "<PERSON><PERSON>, igno<PERSON><PERSON>", "titleImportType": "Standard- oder Vollimport?", "descriptionImportType": "Der Standardimport aktualisiert nur die Normalen Teilnahmemeldungen automatisiert. Der Vollimport aktualisiert den Status aller Teilnahmemeldungen, insofern Sie einer eGK zugewiesen werden können.", "confirmBtnImportTypeBasic": "Standardimport", "confirmBtnImportTypeFull": "Vollimport"}, "dataImported": "Daten wurden importiert für {{contract}}", "dataImportFailed": "Daten konnten nicht importiert werden. Bitte versuchen Sie es erneut.", "totalAmountContractAfterImport": "Gesamtanzahl Verträge {{contract}} nach Import: {{count}}", "fullImportDescription": "„Diese Patienten konnten beim Import nicht eindeutig identifiziert werden. Wurde ein Patient nicht eindeutig identifiziert, so ist dieser in der Regel nicht den vorhandenen Stammdaten in Ihrer Praxissoftware zuordenbar. Die Teilnahmeinformationen müssen deshalb manuell bearbeitet werden.“"}, "PtvImportResolve": {"title": "Konfliktbehebung für: {{patientName}}", "note": "Nicht übereinstimmende Daten wurden gefunden", "description": "Die Patientendaten in garrioPRO stimmen nicht mit den vom PTV importierten Daten überein. Bitte wählen Sie die Daten aus, die für diesen Patienten beibehalten werden sollen.", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "dob": "Geburtsdatum", "status": "garrioPRO Status", "beginDateContract": "Beginndatum", "contractEndDate": "End Date", "insuranceNumber": "Versicherungsnummer", "insurance": "Versicherung", "ikNumber": "IK: {{ikN<PERSON>ber}}", "gender": "Geschlecht", "keepCurrent": "GARRIOPRO", "updateTo": " PTV IMPORTED", "selectAll": "Alle auswählen", "reason": "Begründung", "save": "Speichern"}}