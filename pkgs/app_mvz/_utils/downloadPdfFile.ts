export const TEMPLATE_A4_WIDTH_PX = 230;
export const TEMPLATE_WINDOW_WIDTH_PX = 600;
export const PDF_PADDING_X = 5;
export const PDF_PADDING_Y = 5;

export function dataURItoPDFBlob(dataURI: string) {
  const byteString = window.atob(dataURI);
  const arrayBuffer = new ArrayBuffer(byteString.length);
  const int8Array = new Uint8Array(arrayBuffer);
  for (let i = 0; i < byteString.length; i++) {
    int8Array[i] = byteString.charCodeAt(i);
  }
  const blob = new Blob([int8Array], { type: 'application/pdf' });
  return blob;
}

export async function generatePDFBlobFromHTML(contentEle, fileName?: string, options: {
  orientation?: 'p' | 'portrait' | 'l' | 'landscape';
  unit?: 'pt' | 'px' | 'in' | 'mm' | 'cm' | 'ex' | 'em' | 'pc';
  format?: string | number[];
  width?: number;
  windowWidth?: number;
} = {
  orientation: 'p',
  unit: 'pt',
  format: 'a4',
  width: TEMPLATE_A4_WIDTH_PX,
  windowWidth: TEMPLATE_WINDOW_WIDTH_PX,
}) {
  if (!contentEle) {
    throw new Error('HTML element is required to generate a PDF.');
  }

  const { default: jsPDF } = await import('jspdf');
  const doc = new jsPDF(options.orientation, options.unit, options.format);

  doc.html(contentEle, {
    x: PDF_PADDING_X,
    y: PDF_PADDING_Y,
    html2canvas: {
      scale: 1,
      useCORS: true,
      logging: true,
      allowTaint: true,
    },
    width: options.width,
    windowWidth: options.windowWidth,
    callback: (doc) => {
      doc.save(fileName || 'download-pdf.pdf');
    },
  });
}

export function convertBlobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64String = reader.result as string;
      resolve(base64String.split(',')[1]);
    };
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(blob);
  });
}
