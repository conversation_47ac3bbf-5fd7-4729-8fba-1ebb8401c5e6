import {
  BodyTextM,
  BodyTextS,
  Flex,
  H3,
  Svg,
} from '@tutum/design-system/components';
import { Button, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import { COLOR } from '@tutum/design-system/themes/styles';
import { ParticipantDecision } from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { PTVImportTable } from '@tutum/mvz/module_ptv-import/PtvImport.service';
import PtvImportResolveParticipant from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-conflict-import-list/ptv-import-resolve-participant/PtvImportResolveParticipant.styled';
import {
  PTVImportDate,
  PTVImportStatus,
} from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-status/PTVImportStatus';

import React, { useMemo, useState } from 'react';
export interface IPtvConflictImportListProps {
  className?: string;
  conflictParticipant: PTVImportTable[];
  participationDecision: {
    [key: string]: ParticipantDecision;
  };
  setParticipationDecision: React.Dispatch<
    React.SetStateAction<{
      [key: string]: ParticipantDecision;
    }>
  >;
  showHeader?: boolean;
}

function PtvConflictImportList({
  className,
  conflictParticipant,
  participationDecision,
  setParticipationDecision,
  showHeader = true,
}: IPtvConflictImportListProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });

  const [participantDecisionSelectedId, setParticipantDecisionSelectedId] =
    useState<string>('');

  const [isOpenResolveConflict, setOpenResolveConflict] =
    useState<boolean>(false);

  const columnsData = useMemo(() => {
    return [
      {
        width: '250px',
        name: t('firstName'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <div>
                {participantDecisionItem.lastName.localLastName.value},{' '}
                {participantDecisionItem.firstName.localFirstName.value}
              </div>
              <div>
                <PTVImportDate date={participantDecisionItem.dob.hpmDOB} />
              </div>
            </Flex>
          );
        },
      },
      {
        width: '140px',
        name: t('insuranceNumber'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              {
                participantDecisionItem.insuranceNumber.localInsuranceNumber
                  .value
              }
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('status'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.localStatus.value}
              />
              <PTVImportDate
                prefix="Start"
                date={
                  participantDecisionItem.contractBeginDate
                    .localContractBeginDate
                }
              />
              <PTVImportDate
                prefix="End"
                date={
                  participantDecisionItem.contractEndDate.localContractEndDate
                }
              />
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('statusPtv'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.hpmStatus.value}
              />
              <PTVImportDate
                prefix="Start"
                isHpm
                date={
                  participantDecisionItem.contractBeginDate.hpmContractBeginDate
                }
              />
              <PTVImportDate
                prefix="End"
                isHpm
                date={
                  participantDecisionItem.contractEndDate.hpmContractEndDate
                }
              />
            </Flex>
          );
        },
      },
      {
        name: t('reason'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <span>{participantDecisionItem.reason.hpmReason.value}</span>
          );
        },
      },
      {
        width: '160px',
        name: '',
        cell: (participantDecisionItem: ParticipantDecision) => {
          const isResolved =
            participationDecision[participantDecisionItem.id]?.conflictResolved;
          return (
            <Flex justify="space-between" align="center" gap={8}>
              <Button
                className="resolve-conflict-btn"
                intent={!isResolved ? Intent.PRIMARY : Intent.NONE}
                outlined
                minimal
                // disabled={isResolved}
                onClick={() => {
                  setParticipantDecisionSelectedId(participantDecisionItem.id);

                  if (!participationDecision[participantDecisionItem.id]) {
                    setParticipationDecision((prevValues) => ({
                      ...prevValues,
                      [participantDecisionItem.id]: participantDecisionItem,
                    }));
                  }

                  setOpenResolveConflict(true);
                }}
              >
                {t(isResolved ? 'conflictResolved' : 'resolveConflictBtn')}
              </Button>
              <Svg
                src={
                  isResolved
                    ? '/images/check-circle-solid.svg'
                    : '/images/x-circle-solid.svg'
                }
                width={16}
                height={16}
              />
            </Flex>
          );
        },
      },
    ];
  }, [participationDecision]);

  const onSaveResolveParticipant = (participantDecisionItem) => {
    setParticipationDecision((prevValues) => ({
      ...prevValues,
      [participantDecisionItem.id]: {
        ...participantDecisionItem,
        conflictResolved: true,
      },
    }));
    setOpenResolveConflict(false);
  };
  return (
    <Flex column className={className} gap={8}>
      {showHeader && (
        <>
          <Flex column gap={16} mb={16}>
            <H3 color={COLOR.TEXT_PRIMARY_BLACK}>{t('conflictImportTag')}</H3>
            <Flex>
              <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                {t('conflictDescription')}
              </BodyTextM>
            </Flex>
            <Flex align="center" gap={8}>
              <BodyTextS
                className="flex-1"
                color={COLOR.TEXT_SECONDARY_NAVAL2}
                fontWeight={500}
                textTransform="uppercase"
              >
                {t('total')}
              </BodyTextS>
              <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
                {t('patients', {
                  total: conflictParticipant[0].patient.length,
                })}
              </BodyTextM>
            </Flex>
          </Flex>
        </>
      )}

      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        data={conflictParticipant[0].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      {isOpenResolveConflict &&
        !!participationDecision[participantDecisionSelectedId] && (
          <PtvImportResolveParticipant
            participantDecision={
              participationDecision[participantDecisionSelectedId]
            }
            openResolveParticipant
            onCloseResolveParticipant={() => setOpenResolveConflict(false)}
            onSaveResolveParticipant={onSaveResolveParticipant}
          />
        )}
    </Flex>
  );
}

export default PtvConflictImportList;
