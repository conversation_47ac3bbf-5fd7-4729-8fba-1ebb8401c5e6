import {
  BodyTextM,
  BodyTextS,
  Flex,
  H3,
} from '@tutum/design-system/components';
import Table from '@tutum/design-system/components/Table';
import { COLOR } from '@tutum/design-system/themes/styles';
import { ParticipantDecision } from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { PTVImportTable } from '@tutum/mvz/module_ptv-import/PtvImport.service';
import {
  PTVImportDate,
  PTVImportStatus,
} from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-status/PTVImportStatus';

import { useMemo } from 'react';

export interface IPtvAutoImportListProps {
  className?: string;
  autoImport: PTVImportTable[];
}

function PtvAutoImportList({ className, autoImport }: IPtvAutoImportListProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });

  const columnsData = useMemo(() => {
    return [
      {
        width: '250px',
        name: t('firstName'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <div>
                {participantDecisionItem.lastName.localLastName.value},{' '}
                {participantDecisionItem.firstName.localFirstName.value}
              </div>
              <div>
                <PTVImportDate date={participantDecisionItem.dob.localDOB} />
              </div>
            </Flex>
          );
        },
      },
      {
        width: '140px',
        name: t('insuranceNumber'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              {
                participantDecisionItem.insuranceNumber.localInsuranceNumber
                  .value
              }
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('status'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.localStatus.value}
              />
              <PTVImportDate
                prefix="Start"
                date={
                  participantDecisionItem.contractBeginDate
                    .localContractBeginDate
                }
              />
              <PTVImportDate
                prefix="End"
                date={
                  participantDecisionItem.contractEndDate.localContractEndDate
                }
              />
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('statusPtv'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportStatus
                status={participantDecisionItem.status.hpmStatus.value}
              />
              <PTVImportDate
                prefix="Start"
                isHpm
                date={
                  participantDecisionItem.contractBeginDate.hpmContractBeginDate
                }
              />
              <PTVImportDate
                prefix="End"
                isHpm
                date={
                  participantDecisionItem.contractEndDate.hpmContractEndDate
                }
              />
            </Flex>
          );
        },
      },
      {
        width: '140px',
        name: t('requestDate'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportDate
                prefix="Request"
                isHpm
                date={
                  participantDecisionItem.contractEndDate.hpmContractEndDate
                }
              />
            </Flex>
          );
        },
      },
      {
        name: t('reason'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              {participantDecisionItem.reason.hpmReason.value || '--'}
            </Flex>
          );
        },
      },
      {
        width: '150px',
        name: t('rejectDate'),
        cell: (participantDecisionItem: ParticipantDecision) => {
          return (
            <Flex column gap={4}>
              <PTVImportDate
                prefix="Reject"
                isHpm
                date={
                  participantDecisionItem.contractEndDate.hpmContractEndDate
                }
              />
            </Flex>
          );
        },
      },
    ];
  }, []);

  const total = useMemo(() => {
    return autoImport.reduce((total, datum) => {
      return total + datum.patient.length;
    }, 0);
  }, [autoImport]);

  const patientAmount = (index: number) => {
    const total = autoImport[index].patient.length;
    return total === 0 ? t('zeroPatient') : t('patients', { total });
  };

  return (
    <Flex column className={className}>
      <Flex gap={24} mb={16}>
        <Flex column gap={16} className="flex-1">
          <H3 color={COLOR.TEXT_PRIMARY_BLACK}>{t('autoImportTag')}</H3>
          <Flex align="center" gap={8}>
            <BodyTextS
              className="flex-1"
              color={COLOR.TEXT_SECONDARY_NAVAL2}
              fontWeight={500}
              textTransform="uppercase"
            >
              {t('total')}
            </BodyTextS>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
              {t('patients', {
                total,
              })}
            </BodyTextM>
          </Flex>
        </Flex>
        {/* <Flex>
          <Button
            intent={Intent.PRIMARY}
            outlined
            minimal
            data-test-id="test-run-report-download-pdf"
            onClick={() => {}}
          >
            {t('downloadPdf')}
          </Button>
        </Flex> */}
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        data={[]}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[0].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(0)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[0].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[1].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(1)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[1].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[2].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(2)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[2].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[3].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(3)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[3].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        gap={16}
        justify="space-between"
        className="ptv-table-custom-header"
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {autoImport[4].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {patientAmount(4)}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        noTableHead
        persistTableHead
        striped
        data={autoImport[4].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
    </Flex>
  );
}

export default PtvAutoImportList;
