// @ts-nocheck
import {
  BodyTextM,
  BodyTextS,
  Dialog,
  Flex,
  FormGroup2,
  InputSuggestion,
  Svg,
  Tooltip,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { Button, Classes, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import {
  DEFAULT_INPUT_SUGGESTION_STYLE_CONFIG,
  DEFAULT_SELECT_COMPONENT_CONFIG,
} from '@tutum/design-system/consts/react-select-config';
import { getAge } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { PatientParticipationStatus } from '@tutum/hermes/bff/common';
import {
  PatientSearchingResponse,
  SearchingType,
  useMutationSearchPatients,
} from '@tutum/hermes/bff/legacy/app_mvz_patient_search';
import { TypeOfInsurance } from '@tutum/hermes/bff/patient_profile_common';
import { ParticipantDecision } from '@tutum/hermes/bff/ptv_import_common';
import I18n, { IFixedNamespaceTFunction } from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { isEVCase } from '@tutum/mvz/_utils/cardReader';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import PatientOverviewI18n from '@tutum/mvz/locales/en/PatientOverview.json';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { PTVImportTable } from '@tutum/mvz/module_ptv-import/PtvImport.service';
import {
  PTVImportDate,
  PTVImportStatus,
} from '@tutum/mvz/module_ptv-import/ptv-import-protocol/ptv-status/PTVImportStatus';
import { handleCreateNewPatientByKeyCode } from '@tutum/mvz/utils';
import { Field, Form, Formik } from 'formik';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { components } from 'react-select';

const tooltipIcon = '/images/tooltip.svg';

export interface IPtvMissingImportListProps {
  className?: string;
  missingParticipant: PTVImportTable[];
  participationDecision: {
    [key: string]: ParticipantDecision;
  };
  setParticipationDecision: React.Dispatch<
    React.SetStateAction<{
      [key: string]: ParticipantDecision;
    }>
  >;
}

interface AssignedPatientForm {
  selectPatient: PatientSearchingResponse;
}

const readCardIcon = '/images/patient/selector/read-card-icon.svg';

const CustomInput = (props) => <components.Input {...props} maxLength={20} />;

const CustomOption = (
  props,
  t: IFixedNamespaceTFunction<
    keyof typeof PatientOverviewI18n.PatientOverviewList
  >
) => {
  const patient = props.data;
  const birthdateString = getDateOfBirth(patient.dateOfBirth).value;

  return (
    <components.Option {...props}>
      <Flex column auto>
        <Flex w="100%" justify="space-between">
          <BodyTextM whiteSpace="normal">
            {nameUtils.getPatientName(patient)} -{' '}
            <span>{birthdateString} </span>
            <span>
              (
              {patient.dateOfBirth?.isValidDOB
                ? getAge(new Date(patient.dOB)) + ' J.'
                : '?'}
              )
            </span>
          </BodyTextM>
          <div>
            {isEVCase(patient.insuranceInfo, datetimeUtil.now(), undefined) && (
              <Tooltip content={t('notreadcard')} position="bottom">
                <Svg
                  className="icon-card"
                  src={readCardIcon}
                  height={16}
                  width={16}
                />
              </Tooltip>
            )}
          </div>
        </Flex>
        <div className="searchLightResult">
          <span>
            {patient.typeOfInsurance === TypeOfInsurance.Public ? (
              <span className="public">{'K'}</span>
            ) : (
              <span className="private">{'P'}</span>
            )}{' '}
          </span>

          {patient.insuranceNumber && (
            <>• {<span>{patient.insuranceNumber}</span>}</>
          )}
        </div>
      </Flex>
    </components.Option>
  );
};

function PtvMissingImportList({
  className,
  missingParticipant,
  participationDecision,
  setParticipationDecision,
}: IPtvMissingImportListProps) {
  const { t } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol',
  });

  const { t: tAssignPatientDialog } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportProtocol.assignPatientDialog
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportProtocol.assignPatientDialog',
  });

  const { t: tPatientOverviewList } = I18n.useTranslation<
    keyof typeof PatientOverviewI18n.PatientOverviewList
  >({ namespace: 'PatientOverview', nestedTrans: 'PatientOverviewList' });

  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const patientFileStore = usePatientFileStore();

  const inputSuggestionRef = useRef(null);
  const formikRef = useRef(null);
  const searchPatients = useMutationSearchPatients();

  const [assignPatient, setAssignPatient] =
    useState<ParticipantDecision | null>(null);
  const [defaultOptions, setDefaultOptions] = useState([]);

  const handleGetList = async (query, callback) => {
    const resp = await searchPatients.mutateAsync({
      searchingKeyword: query,
      searchingType: SearchingType.PatientName,
    });

    const mapData = (resp.data.patients || []).map((item) => ({
      ...item,
      data: item,
      label: nameUtils.getPatientName(item),
      value: nameUtils.getPatientName(item),
    }));

    callback(mapData);
  };

  const handleOnFocus = (event, form, field) => {
    form.setTouched({
      ...form.touched,
      [field.name]: true,
    });
    handleGetList(event.target.value, (data: PatientSearchingResponse[]) => {
      setDefaultOptions(data);
    });
  };

  const total = useMemo(() => {
    return missingParticipant.reduce((total, datum) => {
      return total + datum.patient.length;
    }, 0);
  }, [missingParticipant]);

  const columnsData = [
    {
      width: '250px',
      name: t('firstName'),
      cell: (participantDecisionItem: ParticipantDecision) => {
        return (
          <Flex column gap={4}>
            <div>
              {participantDecisionItem.firstName.hpmFirstName.value},{' '}
              {participantDecisionItem.lastName.hpmLastName.value}
            </div>
            <div>
              <PTVImportDate date={participantDecisionItem.dob.hpmDOB} />
            </div>
          </Flex>
        );
      },
    },
    {
      width: '140px',
      name: t('insuranceNumber'),
      cell: (participantDecisionItem: ParticipantDecision) => {
        return (
          <span>
            {participantDecisionItem.insuranceNumber.hpmInsuranceNumber.value}
          </span>
        );
      },
    },
    {
      width: '100px',
      name: t('statusPtv'),
      cell: (participantDecisionItem: ParticipantDecision) => {
        return (
          <Flex column gap={4}>
            <PTVImportStatus
              status={participantDecisionItem.status.hpmStatus.value}
            />
            <PTVImportDate
              prefix="Start"
              isHpm
              date={
                participantDecisionItem.contractBeginDate.hpmContractBeginDate
              }
            />
            <PTVImportDate
              prefix="End"
              isHpm
              date={participantDecisionItem.contractEndDate.hpmContractEndDate}
            />
          </Flex>
        );
      },
    },
    {
      width: '180px',
      name: t('note'),
      cell: (participantDecisionItem: ParticipantDecision) => {
        return participantDecisionItem.reason.hpmReason.value || '--';
      },
    },
    {
      name: '',
      selector: 'assign-patient',
      cell: (participantDecisionItem: ParticipantDecision) => {
        return (
          <Flex column w="100%">
            <Flex alignSelf="flex-end">
              <Flex gap={16} align="center">
                {!!participationDecision[participantDecisionItem.id] && (
                  <BodyTextS color={COLOR.TEXT_PRIMARY_BLACK}>
                    {/* {participantDecisionItem.id} */}
                    {t('assignTo', {
                      patientName: nameUtils.getPatientName({
                        firstName:
                          participationDecision[participantDecisionItem.id]
                            .firstName.localFirstName.value,
                        lastName:
                          participationDecision[participantDecisionItem.id]
                            .lastName.localLastName.value,
                      }),
                    })}
                  </BodyTextS>
                )}
                {participantDecisionItem.markAsDone ||
                participantDecisionItem.isProccessing ? (
                  <Button intent={Intent.PRIMARY} outlined minimal disabled>
                    {t('assignPatient')}
                  </Button>
                ) : (
                  <Button
                    intent={Intent.PRIMARY}
                    outlined
                    minimal
                    onClick={() => {
                      setAssignPatient(participantDecisionItem);
                    }}
                  >
                    {t('assignPatient')}
                  </Button>
                )}
              </Flex>
            </Flex>
          </Flex>
        );
      },
    },
  ];

  const columnsIVData = [
    {
      width: '250px',
      name: t('firstName'),
      cell: (participantDecisionItem: ParticipantDecision) => {
        return (
          <Flex column gap={4}>
            <div>
              {participantDecisionItem.lastName.localLastName.value},{' '}
              {participantDecisionItem.firstName.localFirstName.value}
            </div>
            <div>
              <PTVImportDate date={participantDecisionItem.dob.localDOB} />
            </div>
          </Flex>
        );
      },
    },
    {
      width: '140px',
      name: t('insuranceNumber'),
      cell: (participantDecisionItem: ParticipantDecision) => {
        return (
          <span>
            {participantDecisionItem.insuranceNumber.localInsuranceNumber.value}
          </span>
        );
      },
    },
    {
      width: '150px',
      name: t('status'),
      cell: (participantDecisionItem: ParticipantDecision) => {
        return (
          <Flex column gap={4}>
            <PTVImportStatus
              status={participantDecisionItem.status.localStatus.value}
            />
            <PTVImportDate
              prefix="Start"
              date={
                participantDecisionItem.contractBeginDate.localContractBeginDate
              }
              isLocal
            />
            <PTVImportDate
              prefix="End"
              date={
                participantDecisionItem.contractEndDate.localContractEndDate
              }
              isLocal
            />
          </Flex>
        );
      },
    },
  ];

  const initialValues = useMemo(() => {
    return {
      selectPatient: null,
    };
  }, []);

  const checkValidation = async (values: AssignedPatientForm) => {
    const errors = {};

    if (!values.selectPatient) {
      errors['selectPatient'] = tAssignPatientDialog('selectPatientRequired');
    }

    return errors;
  };

  const handleCreatePatient = () => {
    patientFileActions.setPreventRedirectCreatePatient(true);
    handleCreateNewPatientByKeyCode();
    inputSuggestionRef.current.blur();
  };

  const handleAssignNewPatient = () => {
    handleGetList(
      nameUtils.getPatientName(patientFileStore.createPatient.personalInfo),
      (data: PatientSearchingResponse[]) => {
        const matchedData = data.find(
          (datum) => datum.id === patientFileStore.createPatient.patientId
        );

        setDefaultOptions(data);
        formikRef.current.setFieldValue('selectPatient', matchedData);
      }
    );
  };

  const onCloseDialog = () => {
    setAssignPatient(null);
    patientFileActions.setPreventRedirectCreatePatient(false);
    patientFileActions.setCreatePatient(null);
  };

  const updateValueField = <T,>(
    key: string,
    type: 'current' | 'hpm',
    data: T,
    newValue: string | number
  ): T => {
    const isCurrent = type === 'current';
    const assignKey = isCurrent ? `local${key}` : `hpm${key}`;

    return {
      ...data,
      [assignKey]: {
        ...data[assignKey],
        value: newValue,
      },
    } as T;
  };

  const handleSubmit = (values: AssignedPatientForm) => {
    setParticipationDecision((prevValues) => ({
      ...prevValues,
      [assignPatient.id]: {
        ...assignPatient,
        patientId: values.selectPatient.id,
        firstName: updateValueField(
          'FirstName',
          'current',
          assignPatient.firstName,
          values.selectPatient.firstName
        ),
        lastName: updateValueField(
          'LastName',
          'current',
          assignPatient.lastName,
          values.selectPatient.lastName
        ),
        gender: updateValueField(
          'Gender',
          'current',
          assignPatient.gender,
          values.selectPatient.gender
        ),
        dob: updateValueField(
          'DOB',
          'current',
          assignPatient.dob,
          values.selectPatient.dOB
        ),
        status: updateValueField(
          'Status',
          'current',
          assignPatient.status,
          PatientParticipationStatus.PatientParticipation_Active
        ),
        contractBeginDate: updateValueField(
          'ContractBeginDate',
          'current',
          assignPatient.contractBeginDate,
          values.selectPatient.insuranceInfo.startDate
        ),
        insuranceNumber: updateValueField(
          'InsuranceNumber',
          'current',
          assignPatient.insuranceNumber,
          values.selectPatient.insuranceInfo.insuranceNumber
        ),
        ikNumber: updateValueField(
          'IkNumber',
          'current',
          assignPatient.ikNumber,
          values.selectPatient.insuranceInfo.ikNumber
        ),
        reason: updateValueField('Reason', 'current', assignPatient.reason, ''),
      },
    }));
    alertSuccessfully(tAssignPatientDialog('patientAssigned'));
    onCloseDialog();
  };

  useEffect(() => {
    if (patientFileStore.createPatient) {
      handleAssignNewPatient();
    }
  }, [patientFileStore.createPatient]);

  return (
    <Flex column className={className} gap={8}>
      <Flex mb={8}>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
          {t('missingDescription')}
        </BodyTextM>
      </Flex>
      <Flex align="center" gap={8} mb={8}>
        <BodyTextS
          className="flex-1"
          color={COLOR.TEXT_SECONDARY_NAVAL2}
          fontWeight={500}
          textTransform="uppercase"
        >
          {t('total')}
        </BodyTextS>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
          {t('patients', {
            total,
          })}
        </BodyTextM>
      </Flex>
      <Flex align="center" justify="space-between" gap={16}>
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {missingParticipant[0].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {t('patients', {
            total: missingParticipant[0].patient.length,
          })}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsData}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        data={missingParticipant[0].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      <Flex
        align="center"
        justify="space-between"
        gap={16}
        style={{ marginTop: 8 }}
      >
        <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} fontWeight={600}>
          {missingParticipant[1].description}
        </BodyTextM>
        <BodyTextM color={COLOR.TEXT_SECONDARY_NAVAL2}>
          {t('patients', {
            total: missingParticipant[1].patient.length,
          })}
        </BodyTextM>
      </Flex>
      <Table
        className="ptv-table"
        columns={columnsIVData}
        highlightOnHover
        noHeader
        persistTableHead
        striped
        data={missingParticipant[1].patient}
        responsive={false}
        progressPending={false}
        noDataComponent={
          <BodyTextM margin="16px" color={COLOR.TEXT_TERTIARY_SILVER}>
            {t('noResultFound')}
          </BodyTextM>
        }
      />
      {!!assignPatient && (
        <Dialog
          className={className}
          isOpen
          title={tAssignPatientDialog('title')}
          onClose={onCloseDialog}
        >
          <Formik<AssignedPatientForm>
            innerRef={formikRef}
            initialValues={initialValues}
            enableReinitialize
            validate={checkValidation}
            onSubmit={handleSubmit}
          >
            {({ errors, submitCount, touched, dirty, values }) => (
              <Form>
                <div className={Classes.DIALOG_BODY}>
                  <Flex my={24} mx={16} gap={4} column>
                    <FormGroup2
                      label={tAssignPatientDialog('selectPatient')}
                      name="selectPatient"
                      submitCount={submitCount}
                      errors={errors}
                      touched={touched}
                    >
                      <Field name="selectPatient">
                        {({ field, form }) => (
                          <InputSuggestion
                            ref={inputSuggestionRef}
                            defaultInputValue={nameUtils.getPatientName(
                              field.value
                            )}
                            isClearable
                            defaultValue={field.value?.id}
                            defaultOptions={defaultOptions}
                            cacheOptions
                            isLoading={searchPatients.isPending}
                            loadOptions={(query, callback) => {
                              handleGetList(query, callback);
                            }}
                            keyCode="value"
                            menuPlacement="auto"
                            noOptionsMessage={() => (
                              <Flex gap={8} justify="center">
                                <BodyTextM color={COLOR.TEXT_TERTIARY_SILVER}>
                                  {tAssignPatientDialog('noResultFound')}
                                </BodyTextM>
                                <BodyTextM
                                  className="cursor-pointer"
                                  color={COLOR.TEXT_INFO}
                                  fontWeight={600}
                                  onClick={handleCreatePatient}
                                >
                                  {tAssignPatientDialog('createNewPatient')}
                                </BodyTextM>
                              </Flex>
                            )}
                            styles={{
                              ...DEFAULT_INPUT_SUGGESTION_STYLE_CONFIG(),
                            }}
                            components={{
                              ...DEFAULT_SELECT_COMPONENT_CONFIG,
                              Option: (props) =>
                                CustomOption(props, tPatientOverviewList),
                              Input: CustomInput,
                            }}
                            onChange={(data) => {
                              if (
                                !data ||
                                !field.value ||
                                data.id !== field.value.id
                              ) {
                                form.setFieldValue(field.name, data);
                              }
                            }}
                            onFocus={(event) =>
                              handleOnFocus(event, form, field)
                            }
                          />
                        )}
                      </Field>
                    </FormGroup2>
                    {!!values.selectPatient && (
                      <Flex
                        p="8px 16px"
                        gap={4}
                        column
                        style={{
                          backgroundColor: COLOR.BACKGROUND_ZEBRA,
                        }}
                      >
                        <Flex align="center" gap={8}>
                          <BodyTextS
                            className="flex-1"
                            color={COLOR.TEXT_SECONDARY_NAVAL2}
                            fontWeight={500}
                            textTransform="uppercase"
                          >
                            {tAssignPatientDialog('dob')}
                          </BodyTextS>
                          <BodyTextM
                            color={COLOR.TEXT_PRIMARY_BLACK}
                            style={{ flex: 2 }}
                          >
                            {
                              getDateOfBirth(values.selectPatient.dateOfBirth)
                                .value
                            }{' '}
                            {values.selectPatient.dateOfBirth?.isValidDOB
                              ? getAge(new Date(values.selectPatient.dOB)) +
                                ' J.'
                              : '?'}
                            )
                          </BodyTextM>
                        </Flex>
                        <Flex align="center" gap={8}>
                          <BodyTextS
                            className="flex-1"
                            color={COLOR.TEXT_SECONDARY_NAVAL2}
                            fontWeight={500}
                            textTransform="uppercase"
                          >
                            {tAssignPatientDialog('insuranceNumber')}
                          </BodyTextS>
                          <BodyTextM
                            color={COLOR.TEXT_PRIMARY_BLACK}
                            style={{ flex: 2 }}
                          >
                            {values.selectPatient.insuranceNumber}
                          </BodyTextM>
                        </Flex>
                        <Flex gap={8}>
                          <BodyTextS
                            className="flex-1"
                            color={COLOR.TEXT_SECONDARY_NAVAL2}
                            fontWeight={500}
                            textTransform="uppercase"
                          >
                            {tAssignPatientDialog('insuranceInfo')}
                          </BodyTextS>
                          <Flex style={{ flex: 2 }} column gap={4}>
                            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK}>
                              {
                                values.selectPatient.insuranceInfo
                                  .insuranceCompanyName
                              }
                            </BodyTextM>
                            <BodyTextS color={COLOR.TEXT_SECONDARY_NAVAL2}>
                              {tAssignPatientDialog('ik')}{' '}
                              {values.selectPatient.insuranceInfo.ikNumber}
                            </BodyTextS>
                          </Flex>
                        </Flex>
                      </Flex>
                    )}
                  </Flex>
                </div>
                <div className={Classes.DIALOG_FOOTER}>
                  <Flex className={`${Classes.DIALOG_FOOTER_ACTIONS}`} gap={16}>
                    <Button
                      intent={Intent.PRIMARY}
                      outlined
                      minimal
                      onClick={onCloseDialog}
                    >
                      {tButtonActions('cancelText')}
                    </Button>
                    <Button
                      intent={Intent.PRIMARY}
                      type="submit"
                      disabled={!dirty}
                    >
                      {tButtonActions('assignText')}
                    </Button>
                  </Flex>
                </div>
              </Form>
            )}
          </Formik>
        </Dialog>
      )}
    </Flex>
  );
}

export default PtvMissingImportList;
