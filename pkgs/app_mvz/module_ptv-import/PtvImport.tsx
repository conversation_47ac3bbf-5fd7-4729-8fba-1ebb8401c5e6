import {
  BodyTextL,
  BodyTextM,
  BodyTextS,
  Flex,
  H1,
  Svg,
  Tag,
  Tooltip,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { Button, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import { PAGE_SIZE_DEFAULT } from '@tutum/design-system/consts/table';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { Order, Pagination } from '@tutum/hermes/bff/common';
import {
  useMutationGetCodeByDoctor,
  useMutationGetContractByDoctor,
  useQueryGetListPtvImportHistory,
} from '@tutum/hermes/bff/legacy/app_mvz_ptv_import';
import { ImportContractStatus } from '@tutum/hermes/bff/legacy/ptv_import_common';
import {
  ImportContract,
  PtvImportHistory,
} from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import type PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Participants } from './PtvImport.service';
import PtvImportDryRun from './ptv-import-dry-run/PtvImportDryRun.styled';
import PtvImportListContract from './ptv-import-list-contract/PtvImportListContract.styled';
import { generatePDFBlobFromHTML } from '@tutum/mvz/_utils/downloadPdfFile';

export interface IPtvImportProps {
  className?: string;
}

const DownloadIcon = '/images/download-cloud.svg';
const UploadWhite = '/images/upload-white.svg';
const CheckIcon = '/images/check.svg';
const NewIcon = '/images/plus-circle.svg';
const ClockIcon = '/images/clock-brown.svg';

const defaultPage: Pagination = {
  offset: 0,
  max: PAGE_SIZE_DEFAULT,
  sortBy: 'id',
  order: Order.ASC,
};

function PtvImport({ className }: IPtvImportProps) {
  const globalContext = useContext(GlobalContext.instance);
  const employeeMap = globalContext.useGetDoctorMap();
  const contentRef = useRef<HTMLDivElement | null>(null);

  const { t } = I18n.useTranslation<keyof typeof PtvImportI18n>({
    namespace: 'PtvImport',
  });

  const { t: tPtvImportList } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportList
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportList',
  });

  const { t: tPtvImportListContract } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportListContract
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportListContract',
  });

  const [currentEmployee, setCurrentEmployee] =
    useState<IEmployeeProfile | null>(null);
  const [ptvImportHistories, setPtvImportHistories] = useState<
    PtvImportHistory[]
  >([]);
  const [pagination, setPagination] = useState<Pagination>(defaultPage);
  const [total, setTotal] = useState<number>(0);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();
  const [icode, setIcode] = useState<string>('');
  const [isOpenImportContact, setIsOpenImportContact] =
    useState<boolean>(false);
  const [importContacts, setImportContacts] = useState<ImportContract[]>([]);
  const [selectedContract, setSelectedContract] =
    useState<ImportContract | null>(null);
  const [isOpenDryRun, setIsOpenDryRun] = useState<boolean>(false);
  const [selectedParticipants, setSelectedParticipants] =
    useState<Participants | null>(null);
  const [selectedDoctor, setSelectedDoctor] = useState<IEmployeeProfile | null>(
    null
  );
  const [isImported, setImported] = useState<boolean>(false);

  const getListPtvImportHistory = useQueryGetListPtvImportHistory({
    pagination,
  });

  const getRetrievalCodeByDoctor = useMutationGetCodeByDoctor({
    onSuccess: (res) => {
      setIcode(res.data.code);
    },
  });

  const getPtvContractByDoctor = useMutationGetContractByDoctor({
    onSuccess: (res) => {
      setImportContacts(res.data.contracts || []);
      alertSuccessfully(tPtvImportListContract('dataRequested'));
    },
    onError: () => {
      setImportContacts([]);
      alertError(tPtvImportListContract('dataRequestFailed'));
    },
  });

  const columnsData = useMemo(() => {
    return [
      {
        width: '240px',
        name: tPtvImportList('contractId'),
        cell: (row: PtvImportHistory) => (
          <Flex column>
            <BodyTextM>{row.contractId}</BodyTextM>
          </Flex>
        ),
      },
      {
        width: '260px',
        name: tPtvImportList('doctorName'),
        cell: (row: PtvImportHistory) => {
          const doctor = employeeMap.get(row.doctorId);

          return <BodyTextM>{nameUtils.getDoctorName(doctor)}</BodyTextM>;
        },
      },
      {
        width: '154px',
        name: tPtvImportList('status'),
        cell: (ptvImportItem: PtvImportHistory) => {
          const isImported =
            ptvImportItem.status ==
            ImportContractStatus.ImportContractStatus_Done;
          let text = tPtvImportList('importNew');
          let state: 'info' | 'positive' | 'warning' = 'info';
          let icon = NewIcon;
          switch (ptvImportItem.status) {
            case ImportContractStatus.ImportContractStatus_Done:
              text = tPtvImportList('importSuccess');
              state = 'positive';
              icon = CheckIcon;
              break;
            case ImportContractStatus.ImportContractStatus_InProgress:
              text = tPtvImportList('importInProgress');
              state = 'info';
              icon = NewIcon;
              break;
            case ImportContractStatus.ImportContractStatus_Pending:
              text = tPtvImportList('importPending');
              state = 'warning';
              icon = ClockIcon;
              break;
            default:
              text = tPtvImportList('importNew');
              state = 'info';
              icon = NewIcon;
              break;
          }

          return (
            <Tag slStyle="fill" slState={state}>
              <Flex gap={4} align="center">
                <Svg src={icon} width="16px" height="16px" />
                {text}
              </Flex>
            </Tag>
          );
        },
      },
      {
        width: '150px',
        name: tPtvImportList('quarterTime'),
        cell: (ptvImportItem: PtvImportHistory) => {
          return (
            <span>{`Q${ptvImportItem.quarter} ${ptvImportItem.year}`}</span>
          );
        },
      },
      {
        width: '168px',
        name: tPtvImportList('importerName'),
        cell: (ptvImportItem: PtvImportHistory) => {
          const empProfile = employeeMap?.get(ptvImportItem.importerId);
          return <span>{nameUtils.getDoctorName(empProfile)}</span>;
        },
      },
      {
        width: '160px',
        name: tPtvImportList('importerTime'),
        cell: (ptvImportItem: PtvImportHistory) => {
          if (!ptvImportItem.updateTime) {
            return <BodyTextM>-</BodyTextM>;
          }

          return (
            <Flex column gap={4}>
              <BodyTextM>
                {toDateFormat(new Date(ptvImportItem.updateTime), {
                  dateFormat: 'dd.MM.yyyy',
                })}
              </BodyTextM>
              <BodyTextS>
                {toDateFormat(new Date(ptvImportItem.updateTime), {
                  timeFormat: 'hh:mm',
                })}
              </BodyTextS>
            </Flex>
          );
        },
      },
      {
        id: 'action',
        name: '',
        cell: (ptvImportItem: PtvImportHistory) => {
          const isImported = ptvImportItem.importerId;

          return (
            <Flex w="100%" column>
              <Flex alignSelf="flex-end">
                <Button
                  intent="primary"
                  outlined
                  minimal
                  disabled={!isImported}
                  data-test-id="import-row-btn"
                  onClick={() => {
                    if (isImported) {
                      setSelectedParticipants(ptvImportItem);
                      setIsOpenDryRun(true);
                      return;
                    }
                  }}
                >
                  {!isImported ? (
                    <Tooltip content={tPtvImportList('cantImport')}>
                      {tPtvImportList('import')}
                    </Tooltip>
                  ) : (
                    tPtvImportList('import')
                  )}
                </Button>
              </Flex>
            </Flex>
          );
        },
      },
    ];
  }, [employeeMap]);

  const onChangePage = (page: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      offset: (page - 1) * prevValues.max,
    }));
  };

  const onChangeRowsPerPage = (currentRowsPerPage: number) => {
    setPagination((prevValues) => ({
      ...prevValues,
      offset: 0,
      max: currentRowsPerPage,
    }));
  };

  const onCloseDryRun = () => {
    setIsOpenDryRun(false);
    setSelectedContract(null);
    setSelectedParticipants(null);
    getListPtvImportHistory.refetch();
  };

  const onCloseImportContractList = () => {
    setIsOpenImportContact(false);
    setSelectedDoctor(null);
    setIcode('');
    getListPtvImportHistory.refetch();
  };

  const handleDownloadPdf = async () => {
    const contentEle = contentRef.current;

    if (!contentEle) {
      return;
    }

    const tableContent = contentEle.querySelector('.rdt_Table');

    if (!tableContent) {
      return;
    }

    generatePDFBlobFromHTML(tableContent, 'ptv_overview.pdf', {
      orientation: 'l',
      unit: 'pt',
      format: 'a3',
      width: 1600,
      windowWidth: 1600,
    });
  };

  const onOpenImportContractList = () => {
    setIsOpenImportContact(true);
  };

  const handleRequest = (selectedDoctor: IEmployeeProfile | null, icode: string) => {
    setImported(true);
    onGetContractByDoctor(selectedDoctor?.id || '', icode);
  };

  const onGetRetrievalCodeByDoctor = async (selectedDoctor: IEmployeeProfile | null) => {
    if (selectedDoctor) {
      const timeNow = datetimeUtil.now();
      const year = datetimeUtil.getYear(timeNow);
      const quarter = datetimeUtil.getQuarter(timeNow);

      const data = await getRetrievalCodeByDoctor.mutateAsync({
        doctorId: selectedDoctor.id!,
        year,
        quarter,
      });

      if (data.data.code) {
        handleRequest(selectedDoctor, data.data.code);
      }
    } else {
      setIcode('');
    }
  };

  const onGetContractByDoctor = (doctorId: string, code: string) => {
    if (doctorId) {
      const timeNow = datetimeUtil.now();
      const year = datetimeUtil.getYear(timeNow);
      const quarter = datetimeUtil.getQuarter(timeNow);

      getPtvContractByDoctor.mutate({
        doctorId,
        code,
        year,
        quarter,
      });
    } else {
      setImportContacts([]);
    }
  };

  const onResetContractDoctor = () => {
    setImportContacts([]);
  };

  useEffect(() => {
    setCurrentEmployee(currentLoggedinUser);
    
    if (currentLoggedinUser.isDoctor) {
      setSelectedDoctor(currentLoggedinUser);
      onGetRetrievalCodeByDoctor(currentLoggedinUser);
    }
  }, [currentLoggedinUser]);

  useEffect(() => {
    if (getListPtvImportHistory.isSuccess) {
      setPtvImportHistories(getListPtvImportHistory.data.data);
      setTotal(getListPtvImportHistory.data.total);
    }
  }, [getListPtvImportHistory.isSuccess, getListPtvImportHistory.data]);

  return (
    <Flex column className={className}>
      <Flex align="center" className="sl-Header_Section">
        <H1>{t('title')}</H1>
      </Flex>
      <Flex auto column>
        <Flex gap={16} alignSelf="flex-end" m={16}>
          <Button
            intent={Intent.NONE}
            minimal
            outlined
            icon={<Svg src={DownloadIcon} />}
            data-test-id="download-btn"
            onClick={handleDownloadPdf}
          >
            {tPtvImportList('downloaded')}
          </Button>
          <Button
            intent={Intent.PRIMARY}
            icon={<Svg src={UploadWhite} />}
            data-test-id="import-btn"
            onClick={onOpenImportContractList}
          >
            {tPtvImportList('import')}
          </Button>
        </Flex>
        <Flex ref={contentRef} column>
          <Table
            columns={columnsData}
            highlightOnHover
            noHeader
            persistTableHead
            striped
            data={ptvImportHistories}
            responsive={false}
            progressPending={getListPtvImportHistory.isFetching}
            noDataComponent={
              <Flex my={16} column>
                <BodyTextL
                  margin="62px 0 0"
                  fontWeight={600}
                  color={COLOR.TEXT_TERTIARY_SILVER}
                >
                  {tPtvImportList('noResultFound')}
                </BodyTextL>
              </Flex>
            }
            pagination
            paginationServer
            paginationDefaultPage={pagination.offset / pagination.max + 1}
            paginationResetDefaultPage
            paginationPerPage={pagination.max}
            paginationTotalRows={total || 0}
            onChangePage={onChangePage}
            onChangeRowsPerPage={onChangeRowsPerPage}
          />
        </Flex>
      </Flex>
      {isOpenImportContact && (
        <PtvImportListContract
          mapEmployee={employeeMap}
          onCloseImportContractList={onCloseImportContractList}
          isOpenImportListContract={isOpenImportContact}
          iCode={icode}
          isLoading={getPtvContractByDoctor.isPending}
          setICode={setIcode}
          isImported={isImported}
          setImported={setImported}
          selectedDoctor={selectedDoctor}
          setSelectedDoctor={setSelectedDoctor}
          onGetRetrievalCodeByDoctor={onGetRetrievalCodeByDoctor}
          importContracts={importContacts}
          onGetContractByDoctor={onGetContractByDoctor}
          onResetContractDoctor={onResetContractDoctor}
          setSelectedParticipants={setSelectedParticipants}
          setIsOpenDryRun={setIsOpenDryRun}
          selectedContract={selectedContract}
          setSelectedContract={setSelectedContract}
          handleRequest={handleRequest}
        />
      )}
      {isOpenDryRun && (
        <PtvImportDryRun
          isOpenDryRun={isOpenDryRun}
          data={selectedParticipants || null}
          mapEmployee={employeeMap}
          currentEmployee={currentEmployee}
          onCloseDryRun={onCloseDryRun}
        />
      )}
    </Flex>
  );
}

export default PtvImport;
