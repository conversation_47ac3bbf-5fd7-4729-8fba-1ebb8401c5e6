import {
  BodyTextL,
  BodyTextM,
  BodyTextS,
  Flex,
  H2,
  Svg,
  alertError,
  alertSuccessfully,
} from '@tutum/design-system/components';
import { <PERSON><PERSON>, Dialog, Intent } from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import ConfirmDialog from '@tutum/design-system/components/delete-confirm-dialog';
import {
  getCssClass,
  toDateFormat,
} from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { useMutationImportParticipants } from '@tutum/hermes/bff/legacy/app_mvz_ptv_import';
import {
  PTVImportType,
  ParticipantDecision,
  TypeGroupDecision,
} from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import { generatePDFBlobFromHTML } from '@tutum/mvz/_utils/downloadPdfFile';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import {
  PTVImportTable,
  Participants,
} from '@tutum/mvz/module_ptv-import/PtvImport.service';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import { useEffect, useMemo, useRef, useState } from 'react';
import PtvAutoImportList from '../ptv-import-protocol/ptv-auto-import-list/PtvAutoImportList.styled';
import PtvConflictImportList from '../ptv-import-protocol/ptv-conflict-import-list/PtvConflictImportList.styled';
import PtvMissingImportList from '../ptv-import-protocol/ptv-missing-import-list/PtvMissingImportList.styled';
import PtvImportFullConflict from './ptv-import-full-conflict/PtvImportFullConflict.styled';

type SummaryData = {
  unchangedGroup: ParticipantDecision[];
  newGroup: ParticipantDecision[];
  terminatedGroup: ParticipantDecision[];
  requestedGroup: ParticipantDecision[];
  rejectGroup: ParticipantDecision[];
  specialGroup: ParticipantDecision[];
  missingPtvGroup: ParticipantDecision[];
  missingIvGroup: ParticipantDecision[];
  beforeParticipantCount: number;
  afterParticipantCount: number;
};

export interface IPtvImportDryRunProps {
  className?: string;
  isOpenDryRun: boolean;
  data: Participants | null;
  mapEmployee: Map<string, IEmployeeProfile>;
  currentEmployee: IEmployeeProfile | null;
  onCloseDryRun: () => void;
}

const ArrowRightIcon = '/images/arrow-right-inactive.svg';

function PtvImportDryRun({
  className,
  isOpenDryRun,
  data,
  mapEmployee,
  currentEmployee,
  onCloseDryRun,
}: IPtvImportDryRunProps) {
  const { t } = I18n.useTranslation<keyof typeof PtvImportI18n.PtvImportDryRun>(
    {
      namespace: 'PtvImport',
      nestedTrans: 'PtvImportDryRun',
    }
  );

  const { t: tConfirmImportDialog } = I18n.useTranslation<
    keyof typeof PtvImportI18n.PtvImportDryRun.confirmImportDialog
  >({
    namespace: 'PtvImport',
    nestedTrans: 'PtvImportDryRun.confirmImportDialog',
  });

  const { isPending: isImporting, mutate: importParticipantsFunc } =
    useMutationImportParticipants({
      onSuccess: () => {
        setOpenConfirmDialog(false);
        onCloseDryRun();
        alertSuccessfully(
          t('dataImported', {
            contract: data?.contractId,
          })
        );
      },
      onError: () => {
        alertError(t('dataImportFailed'));
      },
    });

  const contentRef = useRef(null);

  const [doctorImported, setDoctorImported] = useState<IEmployeeProfile | null>(
    null
  );
  const [summaryDataDryRun, setSummaryDataDryRun] =
    useState<SummaryData | null>(null);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [openConfirmDialog, setOpenConfirmDialog] = useState<boolean>(false);
  const [selectImportType, setSelectImportType] =
    useState<PTVImportType | null>(null);
  const [openConfirmImportType, setOpenConfirmImportType] =
    useState<boolean>(false);
  const [openConfirmIgnoreConflict, setOpenConfirmIgnoreConflict] =
    useState<boolean>(false);
  const [participationDecision, setParticipationDecision] = useState<{
    [key: string]: ParticipantDecision;
  }>({});

  const nowDate = datetimeUtil.date();
  const updateTime = data?.updateTime ? datetimeUtil.date(data.updateTime) : null;
  const quarter = datetimeUtil.getQuarter(datetimeUtil.dateToMoment());
  const year = datetimeUtil.getYear(datetimeUtil.dateToMoment());

  const columnsData = useMemo(() => {
    return [
      {
        width: '75%',
        name: t('description'),
        cell: (row: PTVImportTable) => <BodyTextM>{row.description}</BodyTextM>,
      },
      {
        width: '25%',
        name: t('numberPatient'),
        cell: (row: PTVImportTable) => (
          <BodyTextM>{row.patient.length}</BodyTextM>
        ),
      },
    ];
  }, []);

  const dataNormalParticipations = useMemo(() => {
    if (!summaryDataDryRun) {
      return [];
    }

    return [
      {
        description: t('unchangedCase'),
        patient: summaryDataDryRun.unchangedGroup,
      },
      {
        description: t('newCase'),
        patient: summaryDataDryRun.newGroup,
      },
      {
        description: t('terminatedCase'),
        patient: summaryDataDryRun.terminatedGroup,
      },
      {
        description: t('requestedCase'),
        patient: summaryDataDryRun.requestedGroup,
      },
      {
        description: t('rejectedCase'),
        patient: summaryDataDryRun.rejectGroup,
      },
    ];
  }, [summaryDataDryRun]);

  const dataSpecialContractParticipations = useMemo(() => {
    if (!summaryDataDryRun) {
      return [];
    }

    return [
      {
        description: t('specialCase'),
        patient: summaryDataDryRun.specialGroup,
      },
    ];
  }, [summaryDataDryRun]);

  const dataPatientsNotFound = useMemo(() => {
    if (!summaryDataDryRun) {
      return [];
    }

    return [
      {
        description: t('missingInPractice'), // data in hpm but not in practice
        patient: summaryDataDryRun.missingPtvGroup,
      },
      {
        description: t('missingInHPM'), // data in practice but not in hpm
        patient: summaryDataDryRun.missingIvGroup,
      },
    ];
  }, [summaryDataDryRun]);

  const dataPatientsBeforeAndAfter = useMemo(() => {
    if (!summaryDataDryRun) {
      return [];
    }

    return [
      {
        description: t('beforeImport'),
        patient: new Array(summaryDataDryRun.beforeParticipantCount).fill(null),
      },
      {
        description: t('afterImport'),
        patient: new Array(summaryDataDryRun.afterParticipantCount).fill(null),
      },
    ];
  }, [summaryDataDryRun]);

  const hasConflictOrNotFoundData = useMemo(() => {
    return (
      summaryDataDryRun &&
      (summaryDataDryRun.specialGroup.length ||
        summaryDataDryRun.missingPtvGroup.length ||
        summaryDataDryRun.missingIvGroup.length)
    );
  }, [summaryDataDryRun]);

  const hasConflictData = useMemo(() => {
    return (summaryDataDryRun?.specialGroup?.length || 0) > 0;
  }, [summaryDataDryRun]);

  const hasUnresolvedConflict = useMemo(() => {
    if (!summaryDataDryRun) return false;
    const hasConflict = summaryDataDryRun?.specialGroup?.length > 0;
    const resolvedConflict = Object.keys(participationDecision).filter(
      (key) => participationDecision[key].conflictResolved
    );
    const hasUnresolved =
      resolvedConflict.length !== summaryDataDryRun?.specialGroup?.length;
    return hasConflict && hasUnresolved;
  }, [summaryDataDryRun, participationDecision]);

  const legends: (keyof typeof PtvImportI18n.PtvImportDryRun)[] = [
    'additionalInfo1',
    'additionalInfo2',
    'additionalInfo3',
  ];

  const renderStep1 = useMemo(() => {
    return (
      <Flex column gap={24} w="100%">
        <Flex column gap={8}>
          <H2 margin="0 0 8px">
            {t('contractId')} {data?.contractId}
          </H2>
          <Flex align="center" gap={8}>
            <BodyTextS
              className="flex-1"
              color={COLOR.TEXT_SECONDARY_NAVAL2}
              fontWeight={500}
              textTransform="uppercase"
            >
              {t('doctor')}
            </BodyTextS>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
              {t('doctorInfo', {
                doctorName: nameUtils.getDoctorName(doctorImported),
                doctorLanr: doctorImported?.lanr,
                doctorHavgVpId: doctorImported?.havgVpId,
              })}
            </BodyTextM>
          </Flex>
          <Flex align="center" gap={8}>
            <BodyTextS
              className="flex-1"
              color={COLOR.TEXT_SECONDARY_NAVAL2}
              fontWeight={500}
              textTransform="uppercase"
            >
              {t('quarterTime')}
            </BodyTextS>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
              Q{quarter} - {year}
            </BodyTextM>
          </Flex>
          <Flex align="center" gap={8}>
            <BodyTextS
              className="flex-1"
              color={COLOR.TEXT_SECONDARY_NAVAL2}
              fontWeight={500}
              textTransform="uppercase"
            >
              {t('importInfo')}
            </BodyTextS>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
              {nameUtils.getDoctorName(currentEmployee)}
            </BodyTextM>
          </Flex>
          <Flex align="center" gap={8}>
            <BodyTextS
              className="flex-1"
              color={COLOR.TEXT_SECONDARY_NAVAL2}
              fontWeight={500}
              textTransform="uppercase"
            >
              {t('importDate')}
            </BodyTextS>
            <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
              {toDateFormat(updateTime || nowDate, {
                dateFormat: 'dd.MM.yyyy',
                timeFormat: 'hh:mm',
              })}
            </BodyTextM>
          </Flex>
        </Flex>

        <Flex column gap={8}>
          <BodyTextL fontWeight={600} color={COLOR.TEXT_PRIMARY_BLACK}>
            {t('titleNormalList')}
          </BodyTextL>
          <Table
            className="ptv-table"
            columns={columnsData}
            highlightOnHover
            noHeader
            persistTableHead
            striped
            data={dataNormalParticipations}
            responsive={false}
            progressPending={false}
          />
        </Flex>

        <Flex column gap={8}>
          <Flex gap={4}>
            <BodyTextL fontWeight={600} color={COLOR.TEXT_PRIMARY_BLACK}>
              {t('titleSpecialList')}
            </BodyTextL>
            <BodyTextM fontWeight={600} color={COLOR.TEXT_INFO}>
              {'[1] [2]'}
            </BodyTextM>
          </Flex>
          <Table
            className="ptv-table"
            columns={columnsData}
            highlightOnHover
            noHeader
            persistTableHead
            striped
            data={dataSpecialContractParticipations}
            responsive={false}
            progressPending={false}
          />
        </Flex>

        <Flex column gap={8}>
          <Flex gap={4}>
            <BodyTextL fontWeight={600} color={COLOR.TEXT_PRIMARY_BLACK}>
              {t('titleNotFoundList')}
            </BodyTextL>
            <BodyTextM fontWeight={600} color={COLOR.TEXT_INFO}>
              {'[2] [3]'}
            </BodyTextM>
          </Flex>
          <Table
            className="ptv-table"
            columns={columnsData}
            highlightOnHover
            noHeader
            persistTableHead
            striped
            data={dataPatientsNotFound}
            responsive={false}
            progressPending={false}
          />
        </Flex>

        <Flex column gap={8}>
          <BodyTextL fontWeight={600} color={COLOR.TEXT_PRIMARY_BLACK}>
            {t('titleSectionD')}
          </BodyTextL>
          <Table
            className="ptv-table"
            columns={columnsData}
            highlightOnHover
            noHeader
            persistTableHead
            striped
            data={dataPatientsBeforeAndAfter}
            responsive={false}
            progressPending={false}
          />
        </Flex>

        <Flex column gap={8}>
          {legends.map((legend, index) => (
            <Flex key={index} gap={4}>
              <BodyTextM
                fontWeight={600}
                color={COLOR.TEXT_INFO}
                style={{ minWidth: '20px' }}
              >{`[${index + 1}]`}</BodyTextM>
              <BodyTextM>{t(legend)}</BodyTextM>
            </Flex>
          ))}
        </Flex>
      </Flex>
    );
  }, [
    data?.contractId,
    doctorImported,
    currentEmployee,
    columnsData,
    dataNormalParticipations,
    dataSpecialContractParticipations,
    dataPatientsNotFound,
    dataPatientsBeforeAndAfter,
  ]);

  const renderConflictFull = useMemo(() => {
    return (
      <PtvImportFullConflict
        data={data}
        doctorImported={doctorImported}
        currentEmployee={currentEmployee}
        participationDecision={participationDecision}
        setParticipationDecision={setParticipationDecision}
        summaryDataDryRun={summaryDataDryRun}
      />
    );
  }, [
    data,
    doctorImported,
    currentEmployee,
    participationDecision,
    summaryDataDryRun,
  ]);

  const renderStep2 = useMemo(() => {
    return <PtvAutoImportList autoImport={dataNormalParticipations} />;
  }, [dataNormalParticipations]);

  const renderStep3 = useMemo(() => {
    return (
      <PtvConflictImportList
        conflictParticipant={dataSpecialContractParticipations}
        participationDecision={participationDecision}
        setParticipationDecision={setParticipationDecision}
      />
    );
  }, [dataSpecialContractParticipations, participationDecision]);

  const renderStep4 = useMemo(() => {
    return (
      <PtvMissingImportList
        missingParticipant={dataPatientsNotFound}
        participationDecision={participationDecision}
        setParticipationDecision={setParticipationDecision}
      />
    );
  }, [dataPatientsNotFound, participationDecision]);

  const onAutoImport = (data: Participants | null) => {
    if (!data) return;
    if (!selectImportType) return;

    const timeNow = datetimeUtil.now();
    const year = datetimeUtil.getYear(timeNow);
    const quarter = datetimeUtil.getQuarter(timeNow);

    importParticipantsFunc({
      ...data,
      year: year,
      quarter: quarter,
      importType: selectImportType,
    });
  };

  const handleDownloadPdf = async () => {
    const contentEle = contentRef.current;

    if (!contentEle || !data) {
      return;
    }

    generatePDFBlobFromHTML(contentEle, `${data.contractId}.pdf`);
  };

  useEffect(() => {
    if (data) {
      const specialGroup = data?.conflictParticipants;
      const unchangedGroup: ParticipantDecision[] = [];
      const newGroup: ParticipantDecision[] = [];
      const terminatedGroup: ParticipantDecision[] = [];
      const requestedGroup: ParticipantDecision[] = [];
      const rejectGroup: ParticipantDecision[] = [];
      const missingPtvGroup: ParticipantDecision[] = [];
      const missingIvGroup: ParticipantDecision[] = [];

      for (let i = 0; i < data.autoImportParticipants.length; i++) {
        const datum = data.autoImportParticipants[i];

        switch (datum.typeGroupDecision) {
          case TypeGroupDecision.GeneralGroupNew:
            newGroup.push(datum);
            break;
          case TypeGroupDecision.GeneralGroupUnchanged:
            unchangedGroup.push(datum);
            break;
          case TypeGroupDecision.GeneralGroupTerminated:
            terminatedGroup.push(datum);
            break;
          case TypeGroupDecision.GeneralGroupRequested:
            requestedGroup.push(datum);
            break;
          case TypeGroupDecision.GeneralGroupRejected:
            rejectGroup.push(datum);
            break;
        }
      }

      for (let i = 0; i < data.missingParticipants.length; i++) {
        const datum = data.missingParticipants[i];

        switch (datum.typeGroupDecision) {
          case TypeGroupDecision.MissingGroupPTV:
            missingPtvGroup.push(datum);
            break;
          case TypeGroupDecision.MissingGroupIV:
            missingIvGroup.push(datum);
            break;
        }
      }
      const beforeParticipantCount = data.beforeParticipantCount;
      const afterParticipantCount = data.afterParticipantCount;

      setDoctorImported(mapEmployee.get(data.doctorId) || null);
      setSummaryDataDryRun({
        specialGroup,
        unchangedGroup,
        newGroup,
        terminatedGroup,
        requestedGroup,
        rejectGroup,
        missingPtvGroup,
        missingIvGroup,
        beforeParticipantCount,
        afterParticipantCount,
      });
    }
  }, [mapEmployee, data]);

  const handleNextButtonBasicFlow = () => {
    if (currentStep === 1 && !hasConflictOrNotFoundData) {
      setOpenConfirmDialog(true);
      return;
    }

    if (currentStep === 3 && hasUnresolvedConflict) {
      setOpenConfirmIgnoreConflict(true);
      return;
    }

    if (currentStep === 4) {
      setOpenConfirmDialog(true);
      return;
    }

    setCurrentStep(currentStep + 1);
  };

  const handleNextButtonFullFlow = () => {
    if (currentStep === 1 && !hasConflictData) {
      setOpenConfirmDialog(true);
      return;
    }

    if (currentStep === 3 && hasUnresolvedConflict) {
      setOpenConfirmIgnoreConflict(true);
      return;
    }

    setOpenConfirmDialog(true);
  };
  return (
    <Dialog
      className={getCssClass(
        'bp5-dialog-fullscreen',
        'bp5-dialog-content-scrollable',
        className
      )}
      title={currentStep === 1 ? t('title') : t('titleBasicImport')}
      isOpen={isOpenDryRun}
      onClose={onCloseDryRun}
      canOutsideClickClose={false}
    >
      <Flex w="100%" column align="center">
        <Flex column w="100%">
          <Flex className="content" ref={contentRef} column p={24}>
            {currentStep === 1 && renderStep1}
            {currentStep === 2 && renderStep2}
            {currentStep === 3 &&
              selectImportType === PTVImportType.ImportType_Full &&
              renderConflictFull}
            {currentStep === 3 &&
              selectImportType === PTVImportType.ImportType_Basic &&
              renderStep3}
            {currentStep === 4 && renderStep4}
          </Flex>
          <Flex gap={16} alignSelf="flex-end" p={24}>
            {currentStep === 1 ? (
              <Button
                text={t('downloadPdf')}
                intent={Intent.PRIMARY}
                outlined
                minimal
                data-test-id="download-pdf"
                onClick={handleDownloadPdf}
                disabled={isImporting}
              />
            ) : (
              <Button
                text={t('backStep')}
                intent={Intent.PRIMARY}
                outlined
                minimal
                data-test-id="btn-back"
                onClick={() => {
                  if (
                    selectImportType === PTVImportType.ImportType_Full &&
                    currentStep === 3
                  ) {
                    setSelectImportType(null);
                    setCurrentStep(currentStep - 2);
                    return;
                  }

                  if (currentStep === 2) {
                    setSelectImportType(null);
                  }

                  setCurrentStep(currentStep - 1);
                }}
                disabled={isImporting}
              />
            )}
            <Button
              text={t(
                currentStep === 1 && !hasConflictOrNotFoundData
                  ? 'import'
                  : currentStep === 4
                    ? 'updateAll'
                    : 'nextStep'
              )}
              rightIcon={
                (currentStep === 1 && !hasConflictOrNotFoundData) ||
                currentStep === 4 ? null : (
                  <Svg width={10} height={10} src={ArrowRightIcon} />
                )
              }
              intent={Intent.PRIMARY}
              data-test-id={`btn-step-${currentStep}`}
              loading={isImporting}
              disabled={isImporting}
              onClick={() => {
                if (!selectImportType) {
                  setOpenConfirmImportType(true);
                  return;
                }

                if (selectImportType === PTVImportType.ImportType_Basic)
                  handleNextButtonBasicFlow();
                else handleNextButtonFullFlow();
              }}
            />
          </Flex>
        </Flex>
      </Flex>
      <ConfirmDialog
        isOpen={openConfirmImportType}
        text={{
          btnCancel: tConfirmImportDialog('confirmBtnImportTypeBasic'),
          btnOk: tConfirmImportDialog('confirmBtnImportTypeFull'),
          title: tConfirmImportDialog('titleImportType'),
          message: tConfirmImportDialog('descriptionImportType'),
        }}
        intent={Intent.PRIMARY}
        hasIcon={false}
        isLoading={isImporting}
        close={() => {
          setSelectImportType(PTVImportType.ImportType_Basic);
          setOpenConfirmImportType(false);
          setCurrentStep(currentStep + 1);
        }}
        confirm={() => {
          setSelectImportType(PTVImportType.ImportType_Full);
          setOpenConfirmImportType(false);
          setCurrentStep(3)
          // if (hasConflictData) setCurrentStep(3);
          // else setOpenConfirmDialog(true);
        }}
      />
      <ConfirmDialog
        isOpen={openConfirmDialog}
        text={{
          btnCancel: tConfirmImportDialog('cancelBtn'),
          btnOk: tConfirmImportDialog('confirmBtn'),
          title: tConfirmImportDialog('title'),
          message: tConfirmImportDialog('description'),
        }}
        intent={Intent.PRIMARY}
        hasIcon={false}
        isLoading={isImporting}
        close={() => {
          setOpenConfirmDialog(false);
        }}
        confirm={() => {
          const newData: Participants = {
            ...data,
            id: data?.id || '',
            doctorId: data?.doctorId || '',
            contractId: data?.contractId || '',
            documentId: data?.documentId || '',
            autoImportParticipants: data?.autoImportParticipants || [],
            beforeParticipantCount: data?.beforeParticipantCount || 0,
            afterParticipantCount: data?.afterParticipantCount || 0,
            conflictParticipants:
              data?.conflictParticipants?.map((datum) => {
                if (!!participationDecision[datum.id]) {
                  return participationDecision[datum.id];
                }

                return datum;
              }) || [],
            missingParticipants:
              data?.missingParticipants?.map((datum) => {
                if (!!participationDecision[datum.id]) {
                  return participationDecision[datum.id];
                }

                return datum;
              }) || [],
          };
          onAutoImport(newData);
        }}
      />
      <ConfirmDialog
        isOpen={openConfirmIgnoreConflict}
        text={{
          btnCancel: tConfirmImportDialog('cancelBtnIgnoreConflict'),
          btnOk: tConfirmImportDialog('confirmBtnIgnoreConflict'),
          title: tConfirmImportDialog('titleIgnoreConflict'),
          message: tConfirmImportDialog('descriptionIgnoreConflict'),
        }}
        intent={Intent.DANGER}
        hasIcon={false}
        close={() => {
          setOpenConfirmIgnoreConflict(false);
        }}
        confirm={() => {
          setOpenConfirmIgnoreConflict(false);
          if (selectImportType === PTVImportType.ImportType_Basic)
            setCurrentStep(currentStep + 1);
          else {
            setOpenConfirmDialog(true);
          }
        }}
      />
    </Dialog>
  );
}

export default PtvImportDryRun;
