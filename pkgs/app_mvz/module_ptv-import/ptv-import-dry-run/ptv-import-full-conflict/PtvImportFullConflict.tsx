import {
  BodyTextM,
  BodyTextS,
  Flex,
  H2,
} from '@tutum/design-system/components';
import { toDateFormat } from '@tutum/design-system/infrastructure/utils';
import { COLOR } from '@tutum/design-system/themes/styles';
import { ParticipantDecision } from '@tutum/hermes/bff/ptv_import_common';
import I18n from '@tutum/infrastructure/i18n';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import nameUtils from '@tutum/infrastructure/utils/name.utils';
import PtvImportI18n from '@tutum/mvz/locales/en/PtvImport.json';
import { Participants } from '@tutum/mvz/module_ptv-import/PtvImport.service';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import React, { useMemo } from 'react';
import type { ParticipantDecisionWithConflict } from './PtvFullImportList';
import PtvFullImportList from './PtvFullImportList.styled';

export interface IPtvImportFullConflictProps {
  className?: string;
  data: Participants | null;
  doctorImported: IEmployeeProfile | null;
  currentEmployee: IEmployeeProfile | null;
  participationDecision: {
    [key: string]: ParticipantDecision;
  };
  setParticipationDecision: React.Dispatch<
    React.SetStateAction<{
      [key: string]: ParticipantDecision;
    }>
  >;
  summaryDataDryRun: {
    specialGroup: ParticipantDecision[];
    unchangedGroup: ParticipantDecision[];
    newGroup: ParticipantDecision[];
    terminatedGroup: ParticipantDecision[];
    requestedGroup: ParticipantDecision[];
    rejectGroup: ParticipantDecision[];
    missingPtvGroup: ParticipantDecision[];
    missingIvGroup: ParticipantDecision[];
    beforeParticipantCount: number;
    afterParticipantCount: number;
  } | null;
}

function PtvImportFullConflict({
  className,
  data,
  doctorImported,
  currentEmployee,
  participationDecision,
  setParticipationDecision,
  summaryDataDryRun,
}: IPtvImportFullConflictProps) {
  const { t } = I18n.useTranslation<keyof typeof PtvImportI18n.PtvImportDryRun>(
    {
      namespace: 'PtvImport',
      nestedTrans: 'PtvImportDryRun',
    }
  );

  const nowDate = datetimeUtil.date();
  const updateTime = data?.updateTime
    ? datetimeUtil.date(data.updateTime)
    : null;
  const quarter = datetimeUtil.getQuarter(datetimeUtil.dateToMoment());
  const year = datetimeUtil.getYear(datetimeUtil.dateToMoment());

  // Merge all participants into a single array
  const allParticipants = useMemo(() => {
    if (!summaryDataDryRun) return [];

    const merged: ParticipantDecisionWithConflict[] = [];

    // Add all participant groups with hasConflict flag for special group
    summaryDataDryRun.specialGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: true });
    });

    summaryDataDryRun.unchangedGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    summaryDataDryRun.newGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    summaryDataDryRun.terminatedGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    summaryDataDryRun.requestedGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    summaryDataDryRun.rejectGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    summaryDataDryRun.missingPtvGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    summaryDataDryRun.missingIvGroup.forEach((participant) => {
      merged.push({ ...participant, hasConflict: false });
    });

    return merged;
  }, [summaryDataDryRun]);

  return (
    <Flex column gap={24} w="100%" className={className}>
      <Flex column gap={8}>
        <H2 margin="0 0 8px">{t('titleFullImport')}</H2>
        <Flex align="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {t('fullImportDescription')}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('doctor')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {t('doctorInfo', {
              doctorName: nameUtils.getDoctorName(doctorImported),
              doctorLanr: doctorImported?.lanr,
              doctorHavgVpId: doctorImported?.havgVpId,
            })}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('quarterTime')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            Q{quarter} - {year}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('importInfo')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {nameUtils.getDoctorName(currentEmployee)}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextS
            className="flex-1"
            color={COLOR.TEXT_SECONDARY_NAVAL2}
            fontWeight={500}
            textTransform="uppercase"
          >
            {t('importDate')}
          </BodyTextS>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {toDateFormat(updateTime || nowDate, {
              dateFormat: 'dd.MM.yyyy',
              timeFormat: 'hh:mm',
            })}
          </BodyTextM>
        </Flex>
        <Flex align="center" gap={8}>
          <BodyTextM color={COLOR.TEXT_PRIMARY_BLACK} style={{ flex: 6 }}>
            {t('totalAmountContractAfterImport', {
              contract: data?.contractId,
              count: summaryDataDryRun?.afterParticipantCount,
            })}
          </BodyTextM>
        </Flex>
      </Flex>
      <PtvFullImportList
        allParticipants={allParticipants}
        participationDecision={participationDecision}
        setParticipationDecision={setParticipationDecision}
      />
    </Flex>
  );
}

export default PtvImportFullConflict;
