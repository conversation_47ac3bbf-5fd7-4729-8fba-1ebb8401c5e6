import type React from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { LexicalEditor, $nodesOfType } from 'lexical';
import { Field, useField, FormikTouched, FormikErrors } from 'formik';

import type DoctorLetterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';
import { PrivateBillingStatus } from '@tutum/hermes/bff/private_billing_common';
import type { InvoiceInfo } from '@tutum/hermes/bff/legacy/private_billing_common';
import {
  DoctorLetter as DoctorLetterModel,
  InvoiceData,
  LetterTemplate,
  Receiver,
  Sender,
  SenderType,
  Variable,
  ReceiverType,
} from '@tutum/hermes/bff/legacy/doctor_letter_common';
import {
  PAGE_DEFAULT,
  PAGE_SIZE_DEFAULT,
} from '@tutum/design-system/consts/table';
import { useSVFeatureEnable } from '@tutum/mvz/hooks/useSVFeatureEnable';
import type {
  DefaultSidebarD<PERSON>,
  PatientSchein,
} from '@tutum/mvz/module_doctor-letter/doctor-letter-create-edit-dialog/types';
import type { ScheinItem } from '@tutum/hermes/bff/schein_common';

import GlobalContext from '@tutum/mvz/contexts/Global.context';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { TimelineEntityType } from '@tutum/hermes/bff/timeline_common';
import { groupByQuarter } from '@tutum/hermes/bff/legacy/app_mvz_timeline';
import {
  BillingStatus,
  InvoiceInfo as InvoiceInfobG,
} from '@tutum/hermes/bff/bg_billing_common';
import i18n from '@tutum/infrastructure/i18n';
import {
  BodyTextM,
  InfoConfirmDialog,
  LoadingState,
  alertError,
} from '@tutum/design-system/components';
import { FormGroup2 } from '@tutum/design-system/components';
import {
  getDateWithTime,
  getUTCMilliseconds,
} from '@tutum/design-system/infrastructure/utils';
import {
  Category,
  CategoryItemName,
  TemplateType,
} from '@tutum/hermes/bff/doctor_letter_common';
import { AsideContainer } from './Sidebar.styled';
import { TemplateSelect } from './TemplateSelect.component';
import SenderReceiverTemplate from './SenderReceiver/SenderReceiverTemplate';
import ReceiverBilling from './SenderReceiver/SenderReceiverBilling';
import {
  FLUSH_VARIABLES_COMMAND,
  INSERT_SERVICE_CODE_TABLE_COMMAND,
  INSERT_UV_SERVICE_CODE_TABLE_COMMAND,
} from '@tutum/design-system/lexical/components/doctor-letter/OnChange/DoctorLetterOnChange.plugin';
import { SenderSelect } from './SenderSelect.component';
import {
  getBulkInvoiceData,
  useQueryGetHeaderFooters,
  useQueryGetSenderAndReceiver,
} from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import { useQueryGetTemplates } from '@tutum/hermes/bff/legacy/app_doctor_letter';
import { usePrintPreviewPdfStore } from '@tutum/mvz/hooks/usePrintPreviewPdf.store';
import {
  filterTemplatesByPrivateBilling,
  flushReminderVerisonIfNeeded,
  getVariablesFromTimelineModels,
  imperativeFlushVariables,
  toGoaServiceTable,
  toUvGoaServiceTable,
  updateSenderTemplateVariables,
  formatStrNumber,
  VariableWithAdditionalInfo,
  updateReceiverTemplateVariables,
  filterTemplatesByType,
  imparativeFlushBgVariable,
} from '../../helper';
import {
  getBulkGoaServiceTimelines,
  useQueryGetPrivateBillingByScheinId,
} from '@tutum/hermes/bff/legacy/private_billing';
import {
  getUvGoaServiceCodeByIds,
  useQueryGetBgBillingByScheinId,
} from '@tutum/hermes/bff/legacy/bg_billing';
import { useQueryGetPrivateBillingSetting } from '@tutum/hermes/bff/legacy/private_billing_setting';
import { DATE_FORMAT } from '@tutum/mvz/constant/dateTime';
import { VariableNode } from '@tutum/design-system/lexical/components/doctor-letter/Variable';
import { INIT_INVOICE_INFO } from '@tutum/mvz/module_private_billing/PrivateBilling.types';
import {
  checkIsBgSchein,
  checkIsKvSchein,
  checkIsPrivateSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import { getTypeOfLetterTemplate } from '@tutum/admin/module_doctor_letter/DoctorLetter.helper';
import { LetterTemplateType } from '@tutum/admin/module_doctor_letter/DoctorLetter.type';
import { doctorLetterTimelineActions } from '@tutum/mvz/module_doctor-letter/DoctorLetterTimeline.store';
import { useEditors } from '@tutum/mvz/module_doctor-letter/lexical/EditorProvider';
import { INSERT_HEADER_FOOTER_COMMAND } from '@tutum/design-system/lexical/components/doctor-letter/DoctorLetter.constant';
import { getReceiverByType } from '@tutum/mvz/module_doctor-letter/util';
import TimelineDateRange from './TimelineDateRange/TimelineDateRange';
import { Order } from '@tutum/hermes/bff/common';
import { useQueryGetScheinItemByIds } from '@tutum/hermes/bff/legacy/app_mvz_schein';

export const Sidebar: React.FC<{
  patientId: string;
  loadDefaultData: boolean;
  selectedTemplate?: LetterTemplate;
  touched?: FormikTouched<DoctorLetterModel>;
  activeSchein: ScheinItem;
  errors?: FormikErrors<DoctorLetterModel>;
  onTemplateChange: (template?: LetterTemplate) => void;
  onSenderChange: (sender: Sender) => void;
  onReceiverChange: (receiver: Receiver, bulkBillingId?: string) => void;
  onTimelineRangeChange: (
    startDate: number | null,
    endDate: number | null,
    bulkBillingId?: string
  ) => void;
  onlyBulkStager?: boolean;
  defaultSidebarData?: DefaultSidebarData;
  onSelectInvoice: (item: PatientSchein) => void;
  handleNotFoundTemplate: () => void;
  mode: 'view' | 'create' | 'edit';
  isDisableTemplate: boolean;
}> = ({
  patientId,
  loadDefaultData,
  selectedTemplate,
  onTemplateChange,
  onSenderChange,
  onReceiverChange,
  onTimelineRangeChange,
  touched,
  errors,
  activeSchein,
  onlyBulkStager,
  onSelectInvoice,
  defaultSidebarData,
  handleNotFoundTemplate,
  mode,
  isDisableTemplate,
}) => {
  const { editors = [], editorMap } = useEditors();
  const { globalData } = GlobalContext.useContext();
  const { userProfile } = globalData;
  const { t: tDoctorLetter } = i18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({
    namespace: 'DoctorLetter',
  });
  const { t: tTimeLineFormDetail } = i18n.useTranslation({
    namespace: 'PatientManagement',
    nestedTrans: 'TimeLineFormDetail',
  });
  const { t: tCommon } = i18n.useTranslation<unknown>({
    namespace: 'Common',
  });
  const { t: tAddtionalInfo } = i18n.useTranslation({
    namespace: 'Sdebm',
    nestedTrans: 'AdditionalInfo',
  });
  const { selectedBillingItems = [], currentViewInvoice } =
    usePrintPreviewPdfStore();

  const isCurrentScheinPrivate = checkIsPrivateSchein(activeSchein);
  const isKvSchein = checkIsKvSchein(activeSchein);
  const isBgSchein = checkIsBgSchein(activeSchein);

  const templateType =
    selectedTemplate?.type || defaultSidebarData?.doctorLetter.type;

  const [isShowDialog, setIsShowDialog] = useState<boolean>(false);
  const [template, setTemplate] = useState<LetterTemplate | undefined>(
    selectedTemplate
  );
  const [receiverTypeObj, setReceiverTypeObj] = useState<{
    [key: string]: ReceiverType
  }>({});

  const headerFooters = useQueryGetHeaderFooters(
    { query: '', bsnrId: userProfile?.bsnrId },
    {
      enabled: !!userProfile?.bsnrId,
    }
  );

  const { data: resGetSenderAndReceiver } = useQueryGetSenderAndReceiver(
    {
      requests: [
        {
          patientId,
          scheinID: activeSchein.scheinId,
        },
      ],
    },
    {
      enabled: !!patientId && !!activeSchein?.scheinId,
      select: (data) => data?.data?.responses[0],
    }
  );

  const { isExistFAVDoctor } = useSVFeatureEnable();

  const getExcludeTags = () => {
    const excludeTags: string[] = [];
    if (!isExistFAVDoctor) {
      excludeTags.push('FAV');
    }
    return excludeTags;
  };

  const { data: listTemplate } = useQueryGetTemplates(
    {
      paginationRequest: {
        pageSize: PAGE_SIZE_DEFAULT,
        page: PAGE_DEFAULT,
        sortBy: '',
        order: null as unknown as Order,
      },
      query: '',
      excludeTags: getExcludeTags(),
      bsnrId: userProfile?.bsnrId,
    },
    {
      enabled: !!userProfile?.bsnrId,
      throwOnError: false,
    }
  );
  // TODO: better way to findout bsnr name ?
  const bsnrSender = resGetSenderAndReceiver?.senders?.find(
    (s) => s.senderType === SenderType.SenderType_BSNR
  );

  const receivers = resGetSenderAndReceiver?.receivers || [];

  const [isChanged, setIsChanged] = useState(false);
  const [templates, setTemplates] = useState<LetterTemplate[]>([]);

  const isCurrentLetterPrivate =
    getTypeOfLetterTemplate(templateType) === LetterTemplateType.Private;

  const [startDateField, , startDateFieldHelpers] =
    useField('timelineStartDate');
  const [endDateField] = useField('timelineEndDate');
  const [receiverField] = useField('receiver');

  const dateRangeForPrivateSchein = (
    fieldStartDate: number,
    fieldEndDate: number
  ): { startOfQuarterMs: number; endOfQuarterMs: number } => {
    const issuedDate = getDateWithTime(activeSchein.issueDate);
    //  Issued date is in the future
    if (issuedDate.getTime() - datetimeUtil.now() > 0) {
      return {
        startOfQuarterMs: datetimeUtil.startOfSelectedDay(issuedDate).valueOf(),
        endOfQuarterMs: datetimeUtil.endOfSelectedDay(issuedDate).valueOf(),
      };
    }
    const defaultStartDate = fieldStartDate ?? activeSchein.issueDate;
    const defaultEndDate = fieldEndDate ?? +datetimeUtil.endOfSelectedDay();

    return {
      startOfQuarterMs: defaultStartDate,
      endOfQuarterMs: defaultEndDate,
    };
  };

  const privateBillingSetting = useQueryGetPrivateBillingSetting(
    {},
    {
      enabled: isCurrentScheinPrivate,
    }
  );

  const privateBilling = useQueryGetPrivateBillingByScheinId(
    {
      scheinId: activeSchein.scheinId,
    },
    {
      enabled: isCurrentScheinPrivate && !!activeSchein,
    }
  );

  const { data: bgBillingItem } = useQueryGetBgBillingByScheinId(
    {
      scheinId: activeSchein.scheinId,
    },
    {
      enabled: isBgSchein && !!activeSchein,
      select: (data) => data?.data.item,
    }
  );

  const privateBillingItem = privateBilling.data?.item;

  const patientSchein = useMemo(() => {
    if (selectedBillingItems.length) {
      return selectedBillingItems.map((item) => ({
        stager: item.stager,
        context: {
          patientId: item.patient.patientId,
          scheinId:
            item.stager === 'privateBilling'
              ? item.privScheinId
              : item.scheinId,
          invoiceNumber: item.invoiceNumber,
          invoiceId: item.id,
          status: item.status as unknown as BillingStatus,
          invoiceDate: item.invoiceDate,
        },
      }));
    } else {
      if (bgBillingItem) {
        return [
          {
            stager: 'bgBilling',
            context: {
              patientId: patientId,
              scheinId: activeSchein.scheinId,
              invoiceNumber: bgBillingItem.invoiceNumber,
              invoiceId: bgBillingItem.id,
              status: bgBillingItem.status,
              invoiceDate: bgBillingItem.invoiceDate,
            },
          },
        ];
      }
      return [
        {
          stager: 'privateBilling',
          context: {
            patientId: patientId,
            scheinId: activeSchein.scheinId,
            invoiceNumber: privateBillingItem?.invoiceNumber as string,
            invoiceId: privateBillingItem?.id as string,
            status: privateBillingItem?.status as unknown as BillingStatus,
            invoiceDate: privateBillingItem?.invoiceDate as number,
          },
        },
      ];
    }
  }, [
    privateBillingItem,
    selectedBillingItems.length,
    activeSchein,
    bgBillingItem,
    patientId,
  ]);

  const getSenderAndReceiver = useQueryGetSenderAndReceiver({
    requests: patientSchein.map((s) => ({
      patientId: s.context.patientId,
      scheinID: s.context.scheinId,
    })),
  }, {
    enabled: !!patientSchein.length,
    select: (data) => data.data?.responses || [],
  });

  const getScheinItemByIds = useQueryGetScheinItemByIds({
    scheinIds: patientSchein.map((item) => item.context.scheinId),
  }, {
    enabled: !!patientSchein.length,
    select: (data) => data.data?.scheinItems || [],
  });

  const getCurrentOpenEditor = (billingItemId?: string) => {
    if (selectedBillingItems.length === 0 || !billingItemId) {
      return editors?.[0];
    }
    return editorMap[billingItemId];
  };

  const handleInsertGoaServiceTable = (
    editor: LexicalEditor | undefined,
    goaData?: VariableWithAdditionalInfo[][],
    invoiceInfo?: InvoiceInfo
  ) => {
    if (!editor) return;

    let dataTable: VariableWithAdditionalInfo[][] = [];
    let rowTable = '0';
    let invoiceData = INIT_INVOICE_INFO;
    if (goaData?.length) {
      dataTable = goaData;
      rowTable = goaData.length.toString();
    }
    if (invoiceInfo) {
      invoiceData = invoiceInfo;
    }
    const tablePayload = {
      currentBillingStatus: PrivateBillingStatus.PrivBillingStatus_NoInvoice,
      columns: '6',
      includeHeaders: false,
      data: dataTable,
      rows: rowTable,
      invoiceInfo: invoiceData,
    };
    let tableNode: VariableNode | undefined = undefined;
    editor.update(() => {
      tableNode = $nodesOfType(VariableNode).find(
        (node) =>
          node.__variableKey ===
          CategoryItemName.MedicalDocumentation_GoaTable
      );
      tableNode?.select();
    });
    editor.getEditorState().read(() => {
      if (tableNode) {
        editor.dispatchCommand(
          INSERT_SERVICE_CODE_TABLE_COMMAND,
          tablePayload
        );
      }
    });
  };

  const handleInsertUvGoaServiceTable = (
    editor: LexicalEditor | undefined,
    goaData?: VariableWithAdditionalInfo[][],
    invoiceInfo?: InvoiceInfobG
  ) => {
    if (!editor) return;

    let dataTable: VariableWithAdditionalInfo[][] = [];
    let rowTable = '0';

    let invoiceData: InvoiceInfobG = {
      amount: 0,
    };

    if (goaData?.length) {
      dataTable = goaData;
      rowTable = goaData.length.toString();
    }
    if (invoiceInfo) {
      invoiceData = invoiceInfo;
    }
    const tablePayload = {
      currentBillingStatus: BillingStatus.BillingStatus_NoInvoice,
      columns: '6',
      includeHeaders: false,
      data: dataTable,
      rows: rowTable,
      invoiceInfo: invoiceData,
    };
    let tableNode: VariableNode | undefined = undefined;
    editor.update(() => {
      tableNode = $nodesOfType(VariableNode).find(
        (node) =>
          node.__variableKey ===
          CategoryItemName.MedicalDocumentation_UvGoaTable
      );
      tableNode?.select();
    });
    editor.getEditorState().read(() => {
      if (tableNode) {
        editor.dispatchCommand(
          INSERT_UV_SERVICE_CODE_TABLE_COMMAND,
          tablePayload
        );
      }
    });
  };

  const fetchGoaDataForInvoices = useCallback(async () => {
    doctorLetterTimelineActions.setFetchingVariableData(
      'fetchGoaDataForInvoices',
      true
    );
    try {
      const goaServiceResponse = await getBulkGoaServiceTimelines({
        privateBillingIds: patientSchein.map((item) => item.context.invoiceId),
      });
      const goaServiceForEditors =
        goaServiceResponse.data?.data.map((item) => {
          return { ...item, privateBillingId: item.privateBillingId };
        }) || [];

      if (goaServiceForEditors.length === 0) {
        for (const ps of patientSchein) {
          const _editor = getCurrentOpenEditor(ps.context.invoiceId);
          handleInsertGoaServiceTable(_editor);
        }
        return;
      }

      for (const goaPayload of goaServiceForEditors) {
        const goaTimelineEntries = goaPayload.timelineModels ?? [];
        const goaServiceRows = toGoaServiceTable(
          tAddtionalInfo,
          goaTimelineEntries
        );
        const _editor = getCurrentOpenEditor(goaPayload.privateBillingId);
        handleInsertGoaServiceTable(
          _editor,
          goaServiceRows,
          goaPayload.invoiceInfo
        );
      }
    } catch (error) {
      alertError(error.message || 'Failed');
    } finally {
      doctorLetterTimelineActions.setFetchingVariableData(
        'fetchGoaDataForInvoices',
        false
      );
    }
  }, [patientSchein, editors]);

  const fetchUvGoaDataForInvoices = useCallback(async () => {
    doctorLetterTimelineActions.setFetchingVariableData(
      'fetchUvGoaDataForInvoices',
      true
    );
    try {
      const uvGoaServiceResponses = await getUvGoaServiceCodeByIds({
        ids: patientSchein.map(item => item.context.invoiceId),
      });

      for (const datum of patientSchein) {
        const invoiceItem = datum.context;
        const noInvoice =
          invoiceItem?.status === BillingStatus.BillingStatus_NoInvoice;
        const goaServiceForEditors = uvGoaServiceResponses.data.data.find(
          (item) => item.billingId === invoiceItem?.invoiceId
        );

        if (!goaServiceForEditors) {
          return;
        }

        const goaTimelineEntries = !noInvoice
          ? []
          : (goaServiceForEditors.timelineModels ?? []);
        const goaServiceRows = toUvGoaServiceTable(
          tAddtionalInfo,
          goaTimelineEntries
        );
        const _editor = getCurrentOpenEditor(invoiceItem?.invoiceId);
        const scheinItem = getScheinItemByIds.data?.find(schein => schein.scheinId === invoiceItem.scheinId);

        if (scheinItem) {
          await imparativeFlushBgVariable(scheinItem, _editor, patientId, receiverTypeObj[invoiceItem?.invoiceId || '']);
        }

        handleInsertUvGoaServiceTable(_editor, goaServiceRows, {
          ...goaServiceForEditors.invoiceInfo,
          amount: noInvoice ? goaServiceForEditors.invoiceInfo.amount : 0,
        });
      }
    } catch (error) {
      alertError(error.message || 'Failed');
    } finally {
      doctorLetterTimelineActions.setFetchingVariableData(
        'fetchUvGoaDataForInvoices',
        false
      );
    }
  }, [patientSchein, editors, JSON.stringify(receiverTypeObj), getScheinItemByIds.data]);

  useEffect(() => {
    const shouldGetGoaService = privateBillingItem &&
      !privateBillingItem.invoiceDate &&
      privateBillingItem.status ===
        PrivateBillingStatus.PrivBillingStatus_NoInvoice &&
      templateType === TemplateType.TemplateType_Invoice

    if (templates?.length && shouldGetGoaService) {
      fetchGoaDataForInvoices();
    }
  }, [templates?.length, JSON.stringify(privateBillingItem), templateType]);

  useEffect(() => {
    if (templates?.length && !!bgBillingItem) {
      fetchUvGoaDataForInvoices();
    }
  }, [templates?.length, JSON.stringify(bgBillingItem), JSON.stringify(receiverTypeObj)]);

  useEffect(() => {
    if (!privateBillingItem || !templates?.length || !isCurrentLetterPrivate) {
      return;
    }
    patientSchein.forEach(async (s) => {
      if (s.stager === 'privateBilling') {
        const reminderVerison = flushReminderVerisonIfNeeded(
          s.context.status as unknown as PrivateBillingStatus
        );
        const variables: Variable[] = [
          {
            category: Category.Invoice,
            categoryItemName: CategoryItemName.Invoice_Date,
            value: [
              PrivateBillingStatus.PrivBillingStatus_NoInvoice,
              PrivateBillingStatus.PrivBillingStatus_UnPaid,
              PrivateBillingStatus.PrivBillingStatus_1stReminder,
              PrivateBillingStatus.PrivBillingStatus_2ndReminder,
            ].includes(privateBillingItem.status)
              ? datetimeUtil.dateTimeNumberFormat(
                  datetimeUtil.now(),
                  DATE_FORMAT
                )
              : datetimeUtil.dateTimeNumberFormat(
                  s.context.invoiceDate,
                  DATE_FORMAT
                ),
          },
          {
            category: Category.Invoice,
            categoryItemName: CategoryItemName.Invoice_Number,
            value: s.context.invoiceNumber,
          },
        ];

        if (reminderVerison) {
          variables.push({
            category: Category.Invoice,
            categoryItemName: CategoryItemName.Invoice_ReminderHeading,
            value: reminderVerison,
          });
        }
        const editor = getCurrentOpenEditor(s.context.invoiceId);
        imperativeFlushVariables(editor, variables);
      }
    });
  }, [
    editors.length,
    templates,
    isCurrentLetterPrivate,
    privateBillingItem?.id,
  ]);

  const handleChangeTemplate = (opt: LetterTemplate) => {
    if (!opt) return;
    if (isChanged) {
      setTemplate(opt);
      setIsShowDialog(true);
      return;
    }
    onTemplateChange(opt);
    handleReceiverOnChange(receivers, receiverField?.value?.receiverType);
    handleSenderOnChange(SenderType.SenderType_Doctor);
  };

  const templateBySchein = useMemo(() => {
    let listTemplates: LetterTemplate[] = [];
    if (listTemplate) {
      listTemplates = [...listTemplate.letterTemplates];
    }
    if (isKvSchein) {
      return filterTemplatesByType(listTemplates, [
        TemplateType.TemplateType_DoctorLetter,
        TemplateType.TemplateType_Eab,
      ]);
    }
    if (isBgSchein) {
      return filterTemplatesByType(listTemplates, [
        TemplateType.TemplateType_Bg,
      ]);
    }
    return filterTemplatesByPrivateBilling(
      listTemplates,
      privateBilling.data?.item,
      privateBillingSetting.data?.item
    );
  }, [
    selectedBillingItems.length,
    listTemplate,
    privateBilling.data,
    privateBillingSetting.data,
  ]);

  useEffect(() => {
    if (
      !templateBySchein.length ||
      (isCurrentScheinPrivate && !privateBilling.data)
    ) {
      return;
    }
    setTemplates(templateBySchein);

    if (!loadDefaultData || (mode === 'edit' && selectedTemplate)) return;
    if (defaultSidebarData?.doctorLetter.templateId) {
      const defaultTemplateBySchein = templateBySchein.find(
        (t) => t.id === defaultSidebarData.doctorLetter.templateId
      );
      if (!defaultTemplateBySchein?.id) {
        return handleNotFoundTemplate();
      }
      onTemplateChange({
        ...defaultTemplateBySchein,
        body: defaultSidebarData.doctorLetter.body,
      });
    } else {
      onTemplateChange(templateBySchein[0]);
    }
  }, [
    currentViewInvoice,
    loadDefaultData,
    JSON.stringify(templateBySchein),
    isCurrentScheinPrivate,
    defaultSidebarData?.doctorLetter,
    privateBilling.data,
  ]);

  const handleReceiverOnChange = (
    receivers: Receiver[],
    type: ReceiverType
  ) => {
    const currentReceiver = getReceiverByType(receivers, type);
    patientSchein.forEach(async (s) => {
      const editor = getCurrentOpenEditor(s.context.invoiceId);
      updateReceiverTemplateVariables(
        currentReceiver,
        editor,
        templateType,
        s.context.scheinId,
        false,
        patientId
      );
    });
  };

  const handleSenderOnChange = async (opt: SenderType | null) => {
    if (!opt) return;
    doctorLetterTimelineActions.setFetchingVariableData(opt, true);
    
    if (!getSenderAndReceiver.data) {
      return;
    }

    for (const s of getSenderAndReceiver.data) {
      const item = patientSchein.find(
        (item) => item.context.scheinId === s.scheinID
      );

      if (!item) {
        return;
      }

      const editor = getCurrentOpenEditor(item.context.invoiceId);
      const sender = s.senders.find((s) => s.senderType === opt);

      if (!sender || !editor) {
        return;
      }

      const scheinItem = getScheinItemByIds.data?.find(schein => schein.scheinId === s.scheinID);

      onSenderChange(sender);
      updateSenderTemplateVariables(
        s,
        templateType,
        sender,
        editor,
        scheinItem
      );
    }
    doctorLetterTimelineActions.setFetchingVariableData(opt, false);
  };

  useEffect(() => {
    patientSchein.forEach((ps) => {
      const _editor = getCurrentOpenEditor(ps.context.invoiceId);
      if (selectedTemplate && _editor) {
        if (selectedTemplate.headerFooterID) {
          const items = headerFooters.data?.headerFooters || [];
          const headerFooterItem =
            items.find((item) => item.id === selectedTemplate.headerFooterID) ||
            null;
          _editor.dispatchCommand(
            INSERT_HEADER_FOOTER_COMMAND,
            headerFooterItem
          );
        } else {
          _editor.dispatchCommand(INSERT_HEADER_FOOTER_COMMAND, null);
        }
      }
    });
  }, [
    headerFooters.data?.headerFooters,
    selectedBillingItems,
    selectedTemplate,
  ]);

  const getCurrentDaterange = (isPrivateSchein: boolean) => {
    if (isPrivateSchein) {
      return dateRangeForPrivateSchein(
        startDateField?.value,
        endDateField?.value
      );
    }
    const currentDate = datetimeUtil.dateToMoment();
    const startOfQuarterMs =
      datetimeUtil.getStartOfQuarter(currentDate).unix() * 1000;
    const endOfQuarterMs =
      datetimeUtil.getEndOfQuarter(currentDate).unix() * 1000;
    return { startOfQuarterMs, endOfQuarterMs };
  };

  const onTimelineRangeChangeForBulk = (
    letters: DoctorLetterModel[] | undefined
  ) => {
    let { startOfQuarterMs, endOfQuarterMs } = getCurrentDaterange(
      isCurrentScheinPrivate
    );
    const selected = (letters || []).find(
      (l) => l.privateInvoice?.privateBillingId === currentViewInvoice?.id
    );
    if (!selected) {
      //  Schein's createdAt
      startOfQuarterMs = activeSchein.issueDate as number;
    }
    const defaultStartDate = selected?.timelineStartDate ?? startOfQuarterMs;
    const defaultEndDate = selected?.timelineEndDate ?? endOfQuarterMs;
    onTimelineRangeChange(
      defaultStartDate,
      defaultEndDate,
      currentViewInvoice?.id || ''
    );
  };

  const onTimelineRangeChangeForOne = (letter: DoctorLetterModel) => {
    const defaultStartDate = letter?.timelineStartDate;
    const defaultEndDate = letter?.timelineEndDate;
    if (defaultStartDate && defaultEndDate) {
      return onTimelineRangeChange(defaultStartDate, defaultEndDate);
    }

    if (activeSchein?.issueDate) {
      const issueDate = activeSchein.issueDate;
      const currentDate = datetimeUtil.now();
      return onTimelineRangeChange(issueDate, issueDate < currentDate ? currentDate : issueDate);
    }

    const { startOfQuarterMs, endOfQuarterMs } = getCurrentDaterange(
      isCurrentScheinPrivate
    );
    onTimelineRangeChange(startOfQuarterMs, endOfQuarterMs);
  };

  useEffect(() => {
    if (!selectedTemplate || !defaultSidebarData) return;
    handleSenderOnChange(
      defaultSidebarData.doctorLetter?.sender?.senderType || null
    );
    const { bulkDoctorLetter, doctorLetter } = defaultSidebarData;
    if (selectedBillingItems.length) {
      return onTimelineRangeChangeForBulk(bulkDoctorLetter);
    }
    onTimelineRangeChangeForOne(doctorLetter);
  }, [
    editors.length,
    selectedTemplate?.id,
    currentViewInvoice,
    isCurrentScheinPrivate,
    selectedBillingItems.length,
    defaultSidebarData,
    activeSchein.scheinId,
    getSenderAndReceiver.data,
    getScheinItemByIds.data,
  ]);

  const bulkFlushInvoiceData = (invoiceData: InvoiceData) => {
    const editor = getCurrentOpenEditor(invoiceData.privateBillingId);
    if (!editor) return;

    const filterInvoiceInfo = (item) => item.category === Category.Invoice;

    let [invoiceVariables, restVariables]: [Variable[], Variable[]] =
      invoiceData.variables.reduce(
        ([pass, fail], element) => {
          return filterInvoiceInfo(element)
            ? [[...pass, element], fail]
            : [pass, [...fail, element]];
        },
        [[], []]
      );

    if (
      defaultSidebarData?.doctorLetter?.receiver ||
      defaultSidebarData?.bulkDoctorLetter?.length
    ) {
      restVariables = restVariables.filter((v) => {
        return (
          v.categoryItemName !== CategoryItemName.General_ReceiverMail &&
          v.categoryItemName !== CategoryItemName.General_ReceiverPhone &&
          v.categoryItemName !== CategoryItemName.General_ReceiverName &&
          v.categoryItemName !== CategoryItemName.General_ReceiverSalutation &&
          v.categoryItemName !== CategoryItemName.General_ReceiverStreetNo &&
          v.categoryItemName !== CategoryItemName.General_ReceiverPostalCodeCity
        );
      });
    }

    imperativeFlushVariables(editor, restVariables);

    //  Handle Invoice data
    if (invoiceVariables?.length) {
      imperativeFlushVariables(
        editor,
        invoiceVariables.map((v) => formatStrNumber(v))
      );
    }
  };

  useEffect(() => {
    if (
      !templates?.length ||
      !isCurrentLetterPrivate ||
      !globalData?.userProfile
    ) {
      return;
    }
    doctorLetterTimelineActions.setFetchingVariableData(Category.Invoice, true);
    getBulkInvoiceData({
      bnsrCode: globalData.userProfile.bsnr as string,
      privateBillingIds: patientSchein
        .filter((item) => !!item.context.invoiceId)
        .map(({ context: { invoiceId } }) => invoiceId as string),
    })
      .then((res) => {
        res.data?.invoiceData.forEach((item) => {
          //  Flush invoice data
          bulkFlushInvoiceData(item);

          //  Handle Sender
          const { bulkDoctorLetter = [] } = defaultSidebarData || {};
          const sender = item.senders.find(
            (s) => s.senderType === bulkDoctorLetter[0]?.sender?.senderType
          );
          if (sender) {
            onSenderChange(sender);
          }
        });
      })
      .finally(() => {
        doctorLetterTimelineActions.setFetchingVariableData(
          Category.Invoice,
          false
        );
      });
  }, [
    editors.length,
    isCurrentLetterPrivate,
    templates?.length,
    globalData?.userProfile?.bsnr,
  ]);

  // dispatch diagnosis variables
  const takeOverTimelineEntries = async (
    startDateValue: number,
    endDateValue: number
  ) => {
    try {
      patientSchein.forEach(async (s) => {
        const startDate = getDateWithTime(startDateValue);
        const endDate = getDateWithTime(endDateValue);
        const response = await groupByQuarter({
          patientId: s.context.patientId,
          isSortByCategory: true,
          isHistoryMode: false,
          fromDate: getUTCMilliseconds(startDate),
          toDate: getUTCMilliseconds(endDate),
          timelineEntityTypes: [
            {
              timelineEntityType:
                TimelineEntityType.TimelineEntityType_MedicinePrescription,
            },
            {
              timelineEntityType:
                TimelineEntityType.TimelineEntityType_Diagnose,
            },
            { timelineEntityType: TimelineEntityType.TimelineEntityType_Note },
            {
              timelineEntityType:
                TimelineEntityType.TimelineEntityType_Diagnose_AD,
            },
            {
              timelineEntityType:
                TimelineEntityType.TimelineEntityType_Diagnose_DD,
            },
            {
              timelineEntityType:
                TimelineEntityType.TimelineEntityType_Anamnese,
            },
            { timelineEntityType: TimelineEntityType.TimelineEntityType_GDT },
          ],
          scheinId: s.context.scheinId,
        });

        const groupByQuarters = response?.data?.groupByQuarters || [];

        const timelineModels = groupByQuarters.flatMap((g) => g.timelineModels);

        const variables: Variable[] = getVariablesFromTimelineModels(
          timelineModels,
          tTimeLineFormDetail
        );
        if (!variables.length) {
          startDateFieldHelpers.setError(tDoctorLetter('noTimelineEntries'));
          return;
        }
        const editor = getCurrentOpenEditor(s.context.invoiceId);
        if (editor) {
          editor.dispatchCommand(FLUSH_VARIABLES_COMMAND, variables);
        }
      });
      // eslint-disable-next-line no-useless-catch
    } catch (error) {
      throw error;
    }
  };

  useEffect(() => {
    if (!startDateField?.value || !endDateField?.value || !selectedTemplate) {
      return;
    }
    takeOverTimelineEntries(startDateField?.value, endDateField?.value);
  }, [selectedTemplate?.id, startDateField?.value, endDateField?.value]);

  const renderReceiverType = (
    editors: LexicalEditor[],
    templateType: TemplateType | undefined,
    defaultData: DefaultSidebarData | undefined
  ) => {
    if (
      templateType &&
      [
        TemplateType.TemplateType_DoctorLetter,
        TemplateType.TemplateType_Eab,
      ].includes(templateType)
    )
      return (
        <SenderReceiverTemplate
          editor={editors[0]}
          scheinId={activeSchein.scheinId}
          patientId={patientId}
          defaultData={defaultData}
          isBgSchein={isBgSchein}
          onReceiverChange={onReceiverChange}
          onTimelineRangeChange={onTimelineRangeChange}
          setIsChanged={setIsChanged}
        />
      );
    if (
      templateType === TemplateType.TemplateType_Invoice ||
      templateType === TemplateType.TemplateType_Bg ||
      templateType === TemplateType.TemplateType_Reminder
    ) {
      return (
        <ReceiverBilling
          mode="edit"
          t={tDoctorLetter}
          defaultData={defaultData}
          patientScheins={patientSchein as PatientSchein[]}
          onSelectInvoice={onSelectInvoice}
          onReceiverChange={onReceiverChange}
          setIsChanged={setIsChanged}
          setReceiverTypeObj={setReceiverTypeObj}
        />
      );
    }
    return <></>;
  };

  const onHandleChangeDateRange = (startDate: Date, endDate: Date) => {
    const fromDate = startDate ? startDate.getTime() : null;
    const toDate = endDate ? endDate.getTime() : null;
    if (selectedBillingItems.length) {
      return onTimelineRangeChange(
        fromDate,
        toDate,
        currentViewInvoice?.id || ''
      );
    }
    onTimelineRangeChange(fromDate, toDate);
  };

  // Used for detect of user edit in lexical
  useEffect(() => {
    const handleKeyDown = () => {
      setIsChanged(true);
    };
    const el = document.getElementById('sl-doctor-letter-print-content');
    if (!el) {
      return;
    }
    el.addEventListener('keydown', handleKeyDown);
    // Clean up the event listener on component unmount
    return () => {
      el.removeEventListener('keydown', handleKeyDown);
    };
  }, []);
  if (
    !editors.length ||
    privateBilling.isLoading ||
    privateBillingSetting.isLoading ||
    getScheinItemByIds.isFetching
  ) {
    return <LoadingState />;
  }

  return (
    <AsideContainer>
      {!onlyBulkStager && (
        <FormGroup2
          name="templateId"
          label={tDoctorLetter('template')}
          isRequired
        >
          <TemplateSelect
            isDisabled={isDisableTemplate}
            t={tCommon}
            templates={templates}
            value={selectedTemplate}
            onChange={handleChangeTemplate}
            defaultValue={
              defaultSidebarData
                ? {
                    id: defaultSidebarData.doctorLetter.templateId,
                    name: defaultSidebarData.doctorLetter.templateName,
                  }
                : undefined
            }
          />
        </FormGroup2>
      )}
      <FormGroup2
        name="timelineStartDate"
        submitCount={1}
        touched={touched}
        errors={errors}
        label={tDoctorLetter('timelineEntry')}
      >
        <TimelineDateRange
          t={tDoctorLetter}
          activeSchein={activeSchein}
          setIsChanged={setIsChanged}
          loading={privateBilling.isFetching}
          dateRangeForPrivateSchein={dateRangeForPrivateSchein}
          onHandleChangeDateRange={onHandleChangeDateRange}
        />
      </FormGroup2>
      <FormGroup2
        name="sender"
        label={tDoctorLetter('sender')}
        errors={errors}
        touched={touched}
      >
        <Field name="sender">
          {({ field }) => (
            <SenderSelect
              disabled={isBgSchein}
              bsnrName={bsnrSender?.bSNRPayload?.name || ''}
              value={field.value?.senderType || SenderType.SenderType_Doctor}
              onChange={(opt) => {
                setIsChanged(true);
                handleSenderOnChange(opt || null);
              }}
            />
          )}
        </Field>
      </FormGroup2>
      {renderReceiverType(
        editors as LexicalEditor[],
        templateType,
        defaultSidebarData
      )}
      <InfoConfirmDialog
        title={tDoctorLetter('changeTemplateWarningTitle')}
        confirmText={tDoctorLetter('changeTemplateConfirmText')}
        cancelText={tDoctorLetter('changeTemplateCancelText')}
        isOpen={isShowDialog}
        onConfirm={() => {
          onTemplateChange(template);
          handleReceiverOnChange(receivers, receiverField?.value?.receiverType);
          handleSenderOnChange(SenderType.SenderType_Doctor);
          setIsChanged(false);
          setIsShowDialog(false);
        }}
        onClose={() => {
          setIsShowDialog(false);
        }}
      >
        <BodyTextM>{tDoctorLetter('changeTemplateWarningContent')}</BodyTextM>
      </InfoConfirmDialog>
    </AsideContainer>
  );
};
