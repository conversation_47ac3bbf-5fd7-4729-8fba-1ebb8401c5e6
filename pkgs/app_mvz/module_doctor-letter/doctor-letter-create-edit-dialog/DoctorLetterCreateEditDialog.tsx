import type {
  LetterT<PERSON>plate,
  DoctorLetter,
} from '@tutum/hermes/bff/doctor_letter_common';
import type Doctor<PERSON>etterI18n from '@tutum/mvz/locales/en/DoctorLetter.json';
import type { TimelineModel } from '@tutum/hermes/bff/timeline_common';
import { useEmployeeStore } from '@tutum/mvz/hooks/useEmployee';
import { useHotkeys } from 'react-hotkeys-hook';
import React, {
  useMemo,
  useCallback,
  useRef,
  useState,
  Ref,
  useEffect,
} from 'react';
import { Formik, Form, FormikProps } from 'formik';

import { DoctorLetterDialog } from '@tutum/design-system/lexical/components/doctor-letter';
import {
  alertSuccessfully,
  alertError,
  LoadingState,
} from '@tutum/design-system/components';
import i18n from '@tutum/infrastructure/i18n';
import { Flex, Button } from '@tutum/design-system/components';
import { ActionsPanel } from './components/ActionsPanel.component';
import { Sidebar } from './components/Sidebar';
import {
  BulkBillingPayload,
  printPreviewPdfActions,
  usePrintPreviewPdfStore,
} from '@tutum/mvz/hooks/usePrintPreviewPdf.store';
import {
  useQueryGetSenderAndReceiver,
  getTemplates,
} from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import { getScheinItemById } from '@tutum/hermes/bff/legacy/app_mvz_schein';
import {
  filterTemplatesByPrivateBilling,
  filterTemplatesByType,
  getLetterFromTimeline,
  prepareBulkSender,
  prepareBulkTimelineDateRange,
} from './helper';
import { getPrivateBillingSetting } from '@tutum/hermes/bff/legacy/private_billing_setting';
import { DoctorLetterExpanded } from '@tutum/mvz/_utils/doctorLetter.type';
import { useDoctorLetterTimelineStore } from '@tutum/mvz/module_doctor-letter/DoctorLetterTimeline.store';
import { EditorMap } from '../lexical/EditorProvider';
import { SenderType, TemplateType } from '@tutum/hermes/bff/legacy/doctor_letter_common';
import { getTypeOfLetterTemplate } from '@tutum/admin/module_doctor_letter/DoctorLetter.helper';
import { LetterTemplateType } from '@tutum/admin/module_doctor_letter/DoctorLetter.type';
import { EditorField, StyledEditor } from './EditorField';
import { TABLE_GOA_SERIVCE_CLASSNAME } from '@tutum/mvz/module_private_billing/PrivateBilling.utils';
import { IActiveSchein, Mode, PatientSchein } from './types';
import { ScheinItem } from '@tutum/hermes/bff/schein_common';
import { BillingItem } from '@tutum/mvz/_utils/doctorLetter.type';

const ActionPanelInReadMode: React.FC<{
  onPrint: () => Promise<void>;
  onClose: () => void;
}> = ({ onPrint, onClose }) => {
  const { t: tDoctorLetter } = i18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({
    namespace: 'DoctorLetter',
  });

  return (
    <Flex w="100%">
      <Flex grow={1} />
      <Flex>
        <Button intent="primary" outlined onClick={onClose}>
          {tDoctorLetter('close')}
        </Button>
        <Button intent="primary" onClick={onPrint}>
          {tDoctorLetter('print')}
        </Button>
      </Flex>
    </Flex>
  );
};

interface DoctorLetterCreateEditDialogProps {
  patientId?: string; // Incase bulk edit mode
  mode: Mode;
  onClose: () => void;
  onPrint: (
    timelineModel?: TimelineModel,
    isPrintOnly?: boolean
  ) => Promise<any>;
  onSubmit?: (
    values: DoctorLetter | DoctorLetterExpanded
  ) => Promise<TimelineModel | null>;
  defaultDoctorLetterValue?: DoctorLetter;
  successToastMessage?: string;
  failToastMessage?: string;
  activatedSchein: IActiveSchein;
  bulkSelected?: BillingItem[];
  timelineSelected?: TimelineModel[];
  onlyBulkStager?: boolean;
  templates?: LetterTemplate[];
  isDisableTemplate?: boolean;
  onBulkPrint?: () => any;
  onBulkActionSuccess?: () => void;
}

const INIT_FORM_VALUES: DoctorLetter = {
  id: undefined,
  body: '',
  templateId: '',
  templateName: '',
  receiver: undefined,
  sender: {
    senderType: SenderType.SenderType_Doctor,
  },
  isPrinted: false,
  timelineStartDate: undefined,
  timelineEndDate: undefined,
  scheinId: undefined,
  type: TemplateType.TemplateType_DoctorLetter,
};

const initBulkPayload = async (
  selectedBills: BillingItem[],
  selectedTimelines: TimelineModel[] | undefined,
  bsnrId: string
) => {
  const [templates, setting] = await Promise.all([
    getTemplates({ query: '', bsnrId }),
    getPrivateBillingSetting({}),
  ]);

  const letterTemplates = templates?.data?.letterTemplates ?? [];
  if (!letterTemplates.length) return;

  const settingItem = setting.data.item;
  const res: BulkBillingPayload[] = [];

  const isReminderTemplate = (templateId?: string) => {
    const { firstReminder, secondReminder, thirdReminder } = settingItem;
    return (
      templateId === firstReminder?.privateReminderTemplateId ||
      templateId === secondReminder?.privateReminderTemplateId ||
      templateId === thirdReminder?.privateReminderTemplateId
    );
  };

  const findTemplateById = (id?: string) =>
    letterTemplates.find((template) => template.id === id);

  for (const billingItem of selectedBills) {
    const { stager, id: billingId, patient } = billingItem;
    const scheinId =
      stager === 'privateBilling'
        ? billingItem.privScheinId
        : billingItem.scheinId;
    const patientId = patient.patientId;
    let doctorLetterId: string | undefined = undefined;
    let templateId: string | undefined;
    let templateName: string | undefined;

    if (stager === 'privateBilling') {
      const [selectedTemplate] = filterTemplatesByPrivateBilling(
        letterTemplates,
        billingItem,
        settingItem
      );
      templateId = selectedTemplate.id;
      templateName = selectedTemplate.name;

      const timelineEntry = selectedTimelines?.find(
        (entry) =>
          entry.doctorLetter?.privateInvoice?.privateBillingId === billingId
      );

      if (timelineEntry && !isReminderTemplate(templateId)) {
        const { doctorLetter } = timelineEntry;
        doctorLetterId = doctorLetter?.id;
        templateId = doctorLetter?.templateId;
      }

      const formValue: Partial<DoctorLetter> = {
        id: doctorLetterId,
        templateId,
        templateName,
      };

      res.push({
        billingId,
        scheinId: scheinId,
        patientId,
        formValue,
      });
    } else {
      const timelineEntry = selectedTimelines?.find(
        (entry) => entry.doctorLetter?.bgInvoice?.billingId === billingId
      );

      if (timelineEntry) {
        doctorLetterId = timelineEntry.doctorLetter?.id;
        const template = findTemplateById(
          timelineEntry.doctorLetter?.templateId
        );
        templateId = template?.id;
        templateName = template?.name;
      }

      const formValue: Partial<DoctorLetter> = {
        id: doctorLetterId,
        templateId,
        templateName,
      };

      res.push({
        billingId,
        scheinId,
        patientId,
        formValue,
      });
    }
  }

  printPreviewPdfActions.InitializeBulkBillingPayload(res);
};

const DoctorLetterCreateEditDialog: React.FC<
  DoctorLetterCreateEditDialogProps
> = ({
  onPrint,
  onClose,
  mode,
  onSubmit,
  patientId,
  bulkSelected = [],
  activatedSchein,
  defaultDoctorLetterValue,
  successToastMessage,
  failToastMessage,
  timelineSelected,
  onlyBulkStager,
  isDisableTemplate = false,
  onBulkPrint,
  onBulkActionSuccess,
}) => {
  const formRef: Ref<FormikProps<DoctorLetter>> = useRef(null);
  const bulkData = useRef<Record<string, Partial<DoctorLetter>>>({});
  const isBulkMode = !!bulkSelected?.length;
  const { userProfile } = useEmployeeStore();
  const { t: tDoctorLetter } = i18n.useTranslation<
    keyof typeof DoctorLetterI18n
  >({ namespace: 'DoctorLetter' });

  const { selectedBillingItems, currentViewInvoice } =
    usePrintPreviewPdfStore();
  const doctorLetterTimelineStore = useDoctorLetterTimelineStore();

  const requests =
    bulkSelected?.map((item) => ({
      patientId: item.patient.patientId,
      scheinID:
        item.stager === 'privateBilling' ? item.privScheinId : item.scheinId,
    })) ?? [];

  const { data: senderAndReceiver, isLoading: isLoadingSenderAndReceiver } =
    useQueryGetSenderAndReceiver(
      { requests },
      {
        enabled: requests.length > 0,
        select: (res) => res.data.responses,
      }
    );

  useEffect(() => {
    if (bulkSelected.length && userProfile?.bsnrId) {
      printPreviewPdfActions.resetBulkBillingPayload();
      initBulkPayload(bulkSelected, timelineSelected, userProfile.bsnrId);
    }
    return () => {
      printPreviewPdfActions.toggleClearTableSelection();
    };
  }, [userProfile?.bsnrId]);

  //  Important: these 3 states control all private template behaviors
  const _patientId: string = String(
    currentViewInvoice?.patient?.patientId ?? patientId
  );

  const defaultFormValues = useMemo<DoctorLetter>(() => {
    if (!defaultDoctorLetterValue || !defaultDoctorLetterValue.type) {
      return INIT_FORM_VALUES;
    }
    return { ...INIT_FORM_VALUES, ...defaultDoctorLetterValue };
  }, [defaultDoctorLetterValue]);

  const isFetchingVariableData = useMemo(() => {
    if (!Object.keys(doctorLetterTimelineStore.fetchingVariableData).length) {
      return true;
    }
    return Object.keys(doctorLetterTimelineStore.fetchingVariableData).some(
      (key) => doctorLetterTimelineStore.fetchingVariableData[key]
    );
  }, [doctorLetterTimelineStore.fetchingVariableData]);

  const [selectedTemplate, setSelectedTemplate] = useState<
    LetterTemplate | undefined
  >();
  const [enableTemplateSwitch, setEnableTemplateSwitch] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);

  const [scheinItem, setScheinItem] = useState<ScheinItem | undefined>();

  useEffect(() => {
    const fetchScheinItem = async () => {
      try {
        if (!activatedSchein?.scheinId) return;

        const res = await getScheinItemById({
          scheinId: activatedSchein.scheinId,
        });

        if (!res.data) {
          return;
        }
        
        setScheinItem(res.data.scheinItems);
      } catch (err) {
        console.error('Failed to fetch schein item:', err);
        throw err;
      }
    };

    fetchScheinItem();
  }, [JSON.stringify(activatedSchein)]);

  const submitBulkPayload = async (
    editorMap: EditorMap = {}
  ): Promise<TimelineModel[] | undefined> => {
    const { data: response } = await getTemplates({ query: '' });
    if (!response.letterTemplates) return;
    const letterTemplates = response.letterTemplates;

    const bulkPayload: DoctorLetterExpanded[] = [];
    const senderReceiverWithInvoiceId = (senderAndReceiver ?? []).reduce(
      (acc, cur, index) => {
        const id = bulkSelected[index]?.id;
        if (id) acc[id] = cur;
        return acc;
      },
      {} as Record<string, NonNullable<typeof senderAndReceiver>[number]>
    );

    for (const invoice of bulkSelected) {
      let templateBySchein: LetterTemplate;
      if (invoice.stager === 'privateBilling') {
        const setting = await getPrivateBillingSetting({});
        if (!setting || !setting.data.item) return;
        templateBySchein = filterTemplatesByPrivateBilling(
          letterTemplates,
          invoice,
          setting.data.item
        )[0];
      } else {
        templateBySchein = filterTemplatesByType(letterTemplates, [
          TemplateType.TemplateType_Bg,
        ])[0];
      }
      const { timelineStartDate, timelineEndDate } =
        prepareBulkTimelineDateRange(
          invoice.id,
          timelineSelected || [],
          bulkData
        );
      const sender = prepareBulkSender(
        formRef,
        invoice.id,
        senderReceiverWithInvoiceId
      );
      const receiver = bulkData.current[invoice.id].receiver;

      const editor = editorMap[invoice.id];
      if (!editor) return;
      const editorBody = JSON.stringify(editor.getEditorState().toJSON());

      if (!templateBySchein?.id) return;

      const templateType =
        invoice.stager === 'privateBilling'
          ? TemplateType.TemplateType_Invoice
          : TemplateType.TemplateType_Bg;

      const scheinId =
        invoice.stager === 'privateBilling'
          ? invoice.privScheinId
          : invoice.scheinId;

      const payload: DoctorLetterExpanded = {
        id: invoice.id,
        body: editorBody,
        templateId: templateBySchein.id,
        isPrinted: false,
        timelineStartDate,
        timelineEndDate,
        receiver,
        sender,
        type: templateType,
        templateName: templateBySchein.name,
        billingId: invoice.id,
        scheinId: scheinId,
        patientId: invoice.patient.patientId,
      };
      bulkPayload.push(payload);
    }

    const timelines = (
      await Promise.all(bulkPayload.map((p) => onSubmit?.(p)))
    ).filter((t): t is TimelineModel => t != null);
    alertSuccessfully(successToastMessage || 'Success');
    return timelines;
  };

  const submitFunc = useCallback(
    async (values: DoctorLetter & { templateType?: TemplateType }) => {
      const isPrivateTemplate =
        getTypeOfLetterTemplate(values.templateType) ===
        LetterTemplateType.Private;

      try {
        const payload: DoctorLetter = {
          id: values.id,
          body: values.body,
          templateId: values.templateId,
          templateName: values.templateName,
          receiver: values.receiver,
          sender: values.sender,
          isPrinted: values.isPrinted,
          timelineStartDate: values.timelineStartDate,
          timelineEndDate: values.timelineEndDate,
          type:
            isPrivateTemplate && !!values.templateType
              ? values.templateType
              : values.type,
          scheinId: values.scheinId,
        };

        const timelineModel = await onSubmit?.(payload);
        alertSuccessfully(successToastMessage || 'Success');
        return timelineModel ?? null;
      } catch (error) {
        alertError(failToastMessage || 'Failed');
        throw error;
      }
    },
    [selectedTemplate, onSubmit]
  );

  const onPrintingHandler = (_isPrinting: boolean) =>
    setIsPrinting(_isPrinting);

  const onSelectInvoice = ({
    context: { scheinId, patientId },
  }: PatientSchein) => {
    const selectedBilling = selectedBillingItems.find(
      (row) =>
        (row.stager === 'privateBilling' && row.privScheinId === scheinId) ||
        (row.stager === 'bgBilling' &&
          row.scheinId === scheinId &&
          row.patient.patientId === patientId)
    );
    printPreviewPdfActions.setCurrentViewInvoice(selectedBilling);
  };

  useHotkeys(
    'alt+down',
    (event) => {
      event.preventDefault();
      if (!currentViewInvoice) return;
      let idx = selectedBillingItems.findIndex(
        (e) => e.id === currentViewInvoice.id
      );
      if (idx === -1) {
        return;
      }
      idx = (idx + 1) % selectedBillingItems.length;
      printPreviewPdfActions.setCurrentViewInvoice(selectedBillingItems[idx]);
    },
    { enableOnContentEditable: true },
    [currentViewInvoice]
  );
  useHotkeys(
    'alt+up',
    (event) => {
      event.preventDefault();
      if (!currentViewInvoice) return;
      let idx = selectedBillingItems.findIndex(
        (e) => e.id === currentViewInvoice.id
      );
      if (idx === -1) {
        return;
      }
      idx =
        (idx - 1 + selectedBillingItems.length) % selectedBillingItems.length;
      printPreviewPdfActions.setCurrentViewInvoice(selectedBillingItems[idx]);
    },
    { enableOnContentEditable: true },
    [currentViewInvoice]
  );

  useEffect(() => {
    if (bulkSelected.length) {
      const firstItem = bulkSelected[0];
      printPreviewPdfActions.setCurrentViewInvoice(firstItem);
    }
  }, [JSON.stringify(bulkSelected)]);

  // Adjust table style
  useEffect(() => {
    const observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'childList') {
          const elements = document.querySelectorAll('table.editor-table');
          if (elements.length > 0) {
            [...elements].map((node) => {
              node.setAttribute('style', 'margin: -1px 0');
              node.classList.contains(TABLE_GOA_SERIVCE_CLASSNAME) &&
                node.setAttribute('contenteditable', 'false');
            });
            observer.disconnect();
            break;
          }
        }
      }
    });
    observer.observe(document, { childList: true, subtree: true });
    return () => {
      observer.disconnect();
    };
  }, []);

  const isCreate = mode === 'create';
  const defaultSidebarData = useMemo(() => {
    const res = defaultFormValues;
    let bulkDoctorLetter: DoctorLetter[] = [];
    if (isBulkMode && timelineSelected) {
      bulkDoctorLetter = timelineSelected
        .map((item) => item.doctorLetter)
        .filter((letter): letter is DoctorLetter => letter !== undefined);
    }
    return {
      timelineSelected,
      doctorLetter: res,
      bulkDoctorLetter,
      isBulkMode,
    };
  }, [isBulkMode, timelineSelected, defaultFormValues]);

  const handleNotFoundTemplate = () => {
    alertError(tDoctorLetter('templateNotFound'));
    return onClose();
  };

  // NOTE: renders only
  if (mode === 'view') {
    return (
      <DoctorLetterDialog
        canEscapeKeyClose={false}
        title={defaultDoctorLetterValue?.templateName}
        isOpen
        onClose={onClose}
        actions={
          <ActionPanelInReadMode
            onPrint={async () => {
              await onPrint(undefined, true);
              onClose();
            }}
            onClose={onClose}
          />
        }
      >
        <EditorField
          disabled
          placeholder=""
          defaultValue={defaultDoctorLetterValue}
          enableTemplateSwitch={false}
          scheinId={scheinItem?.scheinId}
          values={defaultDoctorLetterValue}
        />
      </DoctorLetterDialog>
    );
  }

  if (isLoadingSenderAndReceiver || !scheinItem) {
    return <LoadingState />;
  }

  return (
    <Formik<DoctorLetter>
      // innerRef={isCreate ? formRef : undefined}
      innerRef={formRef}
      onSubmit={submitFunc}
      initialValues={defaultFormValues}
      validate={function (value) {
        const errors: any = {};
        if (!value.sender) {
          errors.sender = tDoctorLetter('missingSender');
        }
        return errors;
      }}
    >
      {({
        submitForm,
        setFieldValue,
        isSubmitting,
        touched,
        errors,
        isValid,
        values,
      }: FormikProps<DoctorLetter>) => {
        return (
          <Form>
            <DoctorLetterDialog
              isOpen
              canEscapeKeyClose={false}
              title={tDoctorLetter(isCreate ? 'createNew' : 'editTitle')}
              onClose={onClose}
              sidebar={
                <Sidebar
                  isDisableTemplate={isDisableTemplate}
                  mode={mode}
                  defaultSidebarData={defaultSidebarData}
                  onlyBulkStager={onlyBulkStager}
                  activeSchein={scheinItem || null}
                  loadDefaultData={true}
                  touched={touched}
                  errors={errors}
                  patientId={_patientId}
                  selectedTemplate={selectedTemplate}
                  handleNotFoundTemplate={handleNotFoundTemplate}
                  onTemplateChange={(template) => {
                    if (template) {
                      if (bulkSelected?.length && defaultDoctorLetterValue) {
                        setFieldValue(
                          'templateId',
                          defaultDoctorLetterValue.templateId
                        );
                        setSelectedTemplate({
                          id: defaultDoctorLetterValue.templateId,
                          name: defaultDoctorLetterValue.templateName,
                          body: defaultDoctorLetterValue.body,
                          type: defaultDoctorLetterValue.type,
                          variables: template.variables,
                        });
                        if (isCreate) {
                          setFieldValue(
                            'templateName',
                            defaultDoctorLetterValue.templateName
                          );
                          setFieldValue(
                            'templateType',
                            defaultDoctorLetterValue.type
                          );
                          setFieldValue(
                            'templateId',
                            defaultDoctorLetterValue.templateId
                          );
                          setFieldValue(
                            'timelineStartDate',
                            defaultDoctorLetterValue.timelineStartDate
                          );
                          setFieldValue(
                            'timelineEndDate',
                            defaultDoctorLetterValue.timelineEndDate
                          );
                        }
                      } else {
                        setSelectedTemplate(template);
                        setFieldValue('templateId', template.id);
                        setFieldValue('templateName', template.name);
                        setFieldValue('type', template.type);
                      }
                    }
                    if (!isCreate) {
                      setEnableTemplateSwitch(true);
                    }
                  }}
                  onSenderChange={(sender) => {
                    setFieldValue('sender', sender);
                  }}
                  onReceiverChange={(receiver, bulkBillingId) => {
                    if (bulkBillingId) {
                      bulkData.current = {
                        ...bulkData.current,
                        [bulkBillingId]: {
                          ...bulkData.current[bulkBillingId],
                          receiver,
                        },
                      };
                      return;
                    }
                    setFieldValue('receiver', receiver);
                  }}
                  onTimelineRangeChange={(
                    startDate,
                    endDate,
                    bulkBillingId
                  ) => {
                    if (bulkBillingId) {
                      bulkData.current = {
                        ...bulkData.current,
                        [bulkBillingId]: {
                          ...bulkData.current[bulkBillingId],
                          timelineStartDate: startDate ?? undefined,
                          timelineEndDate: endDate ?? undefined,
                        } as Partial<DoctorLetter>,
                      };
                    }
                    setFieldValue('timelineStartDate', startDate);
                    setFieldValue('timelineEndDate', endDate);
                  }}
                  onSelectInvoice={onSelectInvoice}
                />
              }
              actions={
                <ActionsPanel
                  loading={isSubmitting}
                  isFetchingData={isFetchingVariableData}
                  disable={isSubmitting || !isValid}
                  onClose={onClose}
                  onSave={isBulkMode ? submitBulkPayload : submitForm}
                  onPrint={onPrint}
                  onPrinting={onPrintingHandler}
                  bulkMode={
                    isCreate && bulkSelected.length && timelineSelected
                      ? { bulkSelected, timelineSelected }
                      : undefined
                  }
                  onBulkPrint={onBulkPrint}
                  onBulkActionSuccess={onBulkActionSuccess}
                />
              }
            >
              {isBulkMode ? (
                bulkSelected.map((e) => {
                  const template = getLetterFromTimeline(
                    timelineSelected ?? [],
                    e.id
                  );
                  return (
                    <StyledEditor
                      key={e.id}
                      hidden={currentViewInvoice?.id !== e.id}
                      id={e.id}
                      patientId={e.patient.patientId}
                      enableTemplateSwitch={true}
                      defaultValue={template}
                      selectedTemplate={selectedTemplate}
                      disabled={isCreate ? selectedTemplate == null : false}
                      isPrinting={isPrinting || isFetchingVariableData}
                      placeholder={tDoctorLetter('placeholder')}
                      onChange={(stringifiedState) => {
                        setFieldValue('body', stringifiedState);
                      }}
                      scheinId={e.stager === 'bgBilling' ? e.scheinId : e.privScheinId}
                      senderId={values?.sender?.doctorPayload?.id}
                      values={values}
                    />
                  );
                })
              ) : (
                <StyledEditor
                  patientId={_patientId}
                  enableTemplateSwitch={isCreate || enableTemplateSwitch}
                  defaultValue={defaultFormValues}
                  selectedTemplate={selectedTemplate}
                  disabled={isCreate ? selectedTemplate == null : undefined}
                  isPrinting={isPrinting || isFetchingVariableData}
                  placeholder={tDoctorLetter('placeholder')}
                  onChange={(stringifiedState) => {
                    setFieldValue('body', stringifiedState);
                  }}
                  scheinId={scheinItem?.scheinId}
                  senderId={values?.sender?.doctorPayload?.id}
                  values={values}
                />
              )}
            </DoctorLetterDialog>
          </Form>
        );
      }}
    </Formik>
  );
};

export { DoctorLetterCreateEditDialog };
