import React, {
  useContext,
  useEffect,
  useState,
  useMemo,
  useCallback,
} from 'react';
import type { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import {
  Divider,
  InputGroup,
  Switch,
} from '@tutum/design-system/components/Core';
import Table from '@tutum/design-system/components/Table';
import {
  Form,
  FormAction,
  FormType,
  FormName,
} from '@tutum/hermes/bff/form_common';
import I18n from '@tutum/infrastructure/i18n';
import { JobStatus } from '@tutum/hermes/bff/patient_profile_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { EMPLOYMENT_CONTRACT_RULE_5 } from '@tutum/mvz/constant/form';
import useToaster from '@tutum/mvz/hooks/useToaster';
import {
  useMusterFormDialogStore,
  musterFormDialogActions,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import MusterFormDialog from '@tutum/mvz/module_form/muster-form-dialog/MusterFormDialog.styled';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { ISelectedContractDoctor } from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext.type';
import {
  patientFileActions,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { debounce, intersection } from 'lodash';
import M1ICDNotifyDialog from '../form-dialog/M1ICDNotifyDialog';
import {
  formOverviewActions,
  useFormOverviewStore,
} from './FormOverview.store';
import PrintDateConfirmDialog from './print-date-confirm-dialog/styled';
import {
  checkIsSVDocument,
  genColumns,
  getSettingPrintAction,
} from './setting-table';
import {
  alertError,
  alertSuccessfully,
  Flex,
  InfoConfirmDialog,
  Svg,
  TOASTER_TIMEOUT_CUSTOM,
} from '@tutum/design-system/components';
import { PrintPreviewPdfDialog } from '@tutum/mvz/components/PrintPreviewPdfDialog';
import { Nullable } from '@tutum/design-system/infrastructure/models';
import { getFormIdDisplay, HANDOVER_LETTER_FORM } from './FormOverview.helper';
import {
  PrescribeRequest,
  prescribeV2,
} from '@tutum/hermes/bff/legacy/app_mvz_form';
import { ContractType } from '@tutum/hermes/bff/legacy/common';
import FavAlertDialog from '@tutum/mvz/module_lab/lab-results/dialogs/fav-alert-dialog/FavAlertDialog.styled';
import useStateCallbackInline from '@tutum/mvz/hooks/useStateWithCallBackInline';
import {
  printSettingStore as printSettingsStore,
  FormTypeSetting,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import {
  checkIsBgSchein,
  checkIsPrivateSchein,
  checkIsSvSchein,
} from '@tutum/mvz/_utils/scheinFormat';
import CommonLocales from '@tutum/mvz/locales/en/Common.json';
import HandoverLetterForm from './HandoverLetterForm/HandoverLetterForm.styled';
import { useQueryGetForm1450HandoverPatients } from '@tutum/hermes/bff/legacy/app_mvz_patient_overview';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { BASE_PATH_MVZ } from '@tutum/infrastructure/utils/string.util';
import {
  SVWrapper,
  useSVFeatureEnable,
} from '@tutum/mvz/hooks/useSVFeatureEnable';
import { timelineActions, useTimeLineStore } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import { getRelatedList } from '@tutum/mvz/module_form/muster-form-dialog/form-validation/helper.validation';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import { useQueryGetBgBillingByScheinId } from '@tutum/hermes/bff/legacy/bg_billing';
import {
  getTimelineServiceEntries,
  MAXIMUM_UV_GOA_SERVICE_F9990,
} from '../muster-form/MusterForm.helper';
import { useMutationHandleBgInvoicByForm } from '@tutum/hermes/bff/legacy/app_mvz_doctor_letter';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import { ADDITIONAL_INFO_URL } from '@tutum/mvz/module_form/module_form.util';

export interface IFormOverviewProps {
  className?: string;
  selectedContractDoctor: ISelectedContractDoctor;
  patient: IPatientProfile;
}

const SearchIconSvgURL = '/images/search-sidebar-disable.svg';

const MAXIMUM_CHECK_DAY = 180;
const MAXIMUM_VALIDATE_DAY = 42;

const FormOverview = ({
  className,
  patient,
  selectedContractDoctor,
}: IFormOverviewProps) => {
  const { t } = I18n.useTranslation<Paths<typeof FormI18n>>({
    namespace: 'Form',
  });
  const { t: tCommonInput } = I18n.useTranslation<
    keyof typeof CommonLocales.Input
  >({
    namespace: 'Common',
    nestedTrans: 'Input',
  });
  const { t: tConfirmMaximumUVGoaF9990 } = I18n.useTranslation<
    keyof typeof FormI18n.confirmMaximumUVGoaF9990
  >({
    namespace: 'Form',
    nestedTrans: 'confirmMaximumUVGoaF9990',
  });
  const { t: tConfirmSickLeaveDialog } = I18n.useTranslation<
    keyof typeof FormI18n.confirmSickLeaveDialog
  >({
    namespace: 'Form',
    nestedTrans: 'confirmSickLeaveDialog',
  });

  const [isOpen, setIsOpen] = useState(false);
  const [selectedForm, setSelectedForm] = useState<Form | undefined>(undefined);
  const [formAction, setFormAction] = useState<FormAction | undefined>(undefined);
  const [isOpenM1Notify, setOpenM1Notify] = useState<boolean>(false);
  const [printUrl, setPrintUrl] = useState<string>('');
  const [formId, setFormId] = useState<string>('');
  const [openFavAlertDialog, setOpenFavAlertDialog] =
    useStateCallbackInline(false);
  const [selectedFormName, setSelectedFormName] = useState('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isShowSVDocumentsOnly, setShowSVDocumentsOnly] =
    useState<boolean>(false);
  const [isOpenHandoverLetter, setOpenHandoverLetter] =
    useState<boolean>(false);

  const {
    data: lkkPatients,
    isSuccess,
    refetch,
  } = useQueryGetForm1450HandoverPatients();

  const musterFormDialogStore = useMusterFormDialogStore();
  const {
    loadingListForms,
    listForms,
    patient: patientStore,
    selectedContractDoctor: selectedContractDoctorStore,
  } = useFormOverviewStore();
  const toaster = useToaster();
  const patientFileStore = usePatientFileStore();
  const { patientManagement } = useContext(PatientManagementContext.instance);
  const { isExistSVDoctor } = useSVFeatureEnable();
  const timelineStore = useTimeLineStore();

  const onLoadListForms = (isExistSVDoctor: boolean) => {
    formOverviewActions.loadListForms(isExistSVDoctor);
  };

  const [openConfirmSickLeaveDialog, setOpenConfirmSickLeaveDialog] =
    useStateCallbackInline(false);
  const [openConfirmMaximumUVGoaF9990, setOpenConfirmMaximumUVGoaF9990] =
    useStateCallbackInline(false);

  const currentSchein = useCurrentSchein();
  const { isContractSupport: hasSupportForm1414 } = useCheckContractSupport(
    ['FORM1414'],
    [currentSchein?.hzvContractId]
  );

  const getBgBillingByScheinId = useQueryGetBgBillingByScheinId(
    {
      scheinId: currentSchein?.scheinId as string,
    },
    {
      enabled: checkIsBgSchein(currentSchein),
    }
  );

  const mutationHandleBgInvoicByForm = useMutationHandleBgInvoicByForm({
    onSuccess: () => {
      patientFileActions.schein.getScheinsOverview(patient?.id);
    },
  });

  const isShowHintSlickLeaveDuration = useMemo(() => {
    if (!hasSupportForm1414) {
      return false;
    }

    const previousDay = datetimeUtil.subtractByNumberOfDay(MAXIMUM_CHECK_DAY);
    let index = 1;
    const M1FormWithin180Days = timelineStore.timelineState.reduce(
      (data, timelineGroup) => {
        timelineGroup.timelineModels.forEach((timeline) => {
          if (!timeline.encounterForm) {
            return;
          }

          const {
            prescribe: { printedDate, prescribeDate, formName, payload },
          } = timeline.encounterForm;

          if (!printedDate || ![FormName.Muster_1].includes(formName)) {
            return;
          }

          const day = datetimeUtil.diff(
            datetimeUtil.now(),
            prescribeDate,
            'days'
          );

          if (day > MAXIMUM_CHECK_DAY) {
            return;
          }

          const payloadObject = JSON.parse(payload);
          const isFollowUp = payloadObject['checkbox_folgebes_0'];
          const startDate =
            +payloadObject['date_arbeit_0'] > +previousDay
              ? +payloadObject['date_arbeit_0']
              : +previousDay;
          const endDate = payloadObject['date_vorau_0'];
          const list = getRelatedList('label_icd10_code_').map(
            (item) => payloadObject[item]
          );
          const key = list.filter((item) => item).join('_');

          if (!data[`${key}_${index}`]) {
            const value = {
              startDate: isFollowUp ? +previousDay : startDate,
              endDate,
            };

            data[`${key}_${index}`] = [];

            if (isFollowUp) {
              data[`${key}_${index}`].push(null);
            }

            data[`${key}_${index}`].push(value);
          } else if (isFollowUp) {
            data[`${key}_${index}`].push({
              startDate,
              endDate,
            });
            index++;
          } else if (data[`${key}_${index}`][1]) {
            data[`${key}_${index + 1}`] = [];
            data[`${key}_${index + 1}`].push({
              startDate,
              endDate,
            });
            index++;
          }
        });

        return data;
      },
      {}
    );

    const duration = Object.keys(M1FormWithin180Days).reduce((total, key) => {
      const item = M1FormWithin180Days[key];
      const [first, second] = item;
      let day = 0;

      if (first) {
        const { startDate, endDate } = first;

        day = datetimeUtil.diff(
          second ? second.endDate : endDate,
          startDate,
          'days'
        );
      } else {
        const { startDate, endDate } = second;

        day = datetimeUtil.diff(endDate, startDate, 'days');
      }

      total += day;

      return total;
    }, 0);

    return duration + 1 > MAXIMUM_VALIDATE_DAY;
  }, [hasSupportForm1414, timelineStore.timelineState]);

  const hintM10 = useMemo(() => {
    return listForms.find(
      (form) =>
        form.formType === FormType.FormType_contract_hint &&
        form.title === 'Muster 10/10A'
    );
  }, [listForms]);

  useEffect(() => {
    onLoadListForms(isExistSVDoctor);
  }, [
    isExistSVDoctor,
    patientStore?.id,
    selectedContractDoctorStore?.doctorId,
    selectedContractDoctorStore?.contractId,
    JSON.stringify(patientFileStore.schein.activatedSchein),
  ]);

  // Follow rule: https://www.notion.so/silenteer/Prescribe-in-print-preview-f221918e3c894b7b8c30ff5d0c8f7f9e
  const formRulePreprocessM1 = (openFormCallback: () => void) => {
    if (patient) {
      const now = datetimeUtil.date();
      const lastUpdated = new Date(patient.employmentInfoUpdatedAt || 0);
      const durationDate = datetimeUtil
        .getDayDuration(lastUpdated, now)
        .asDays();
      const activeContracts = patientManagement.activeParticipations.map(
        (p) => p.contractId
      );
      if (
        (!String(patient.patientInfo.employmentInfo.jobStatus) ||
          patient.patientInfo.employmentInfo.jobStatus ===
          JobStatus.JobStatus_JobSeeker ||
          durationDate >= 365) &&
        intersection(EMPLOYMENT_CONTRACT_RULE_5, activeContracts).length
      ) {
        setOpenM1Notify(true);
      } else {
        openFormCallback();
      }
    }
  };

  const formRulePreprocess = (formItem: Form, openFormCallback: () => void) => {
    switch (formItem.id) {
      case FormName.Muster_1:
        formRulePreprocessM1(openFormCallback);
        break;
      default:
        openFormCallback();
        break;
    }
  };

  const toastPrintSuccess = () => {
    alertSuccessfully(t('formPrinted'), {
      timeout: TOASTER_TIMEOUT_CUSTOM,
      toaster,
    });
  };

  const handlePrescribe = (formItem: Form) => {
    if (
      [FormName.Muster_10, FormName.Muster_10A].includes(
        formItem.id as FormName
      ) &&
      !!hintM10
    ) {
      setOpenFavAlertDialog(true);
      setSelectedFormName(formItem.id);
      return;
    }

    if (
      [FormName.Muster_1].includes(formItem.id as FormName) &&
      isShowHintSlickLeaveDuration
    ) {
      setOpenConfirmSickLeaveDialog(true);
      return;
    }

    const uvGoaEntries = getTimelineServiceEntries(
      timelineStore.timelineState,
      currentSchein?.scheinId
    );

    if (
      [FormName.F9990].includes(formItem.id as FormName) &&
      uvGoaEntries.length > MAXIMUM_UV_GOA_SERVICE_F9990
    ) {
      setOpenConfirmMaximumUVGoaF9990(true);
      return;
    }

    switch (formItem?.formType) {
      case FormType.FormType_heimi:
        patientFileActions.setActiveTabId(ID_TABS.HEIMI);
        break;
      case FormType.FormType_himi:
        patientFileActions.setActiveTabId(ID_TABS.HIMI);
        patientFileActions.params.set('formId', formItem?.id);
        break;
      case FormType.FormType_medication:
        patientFileActions.setActiveTabId(ID_TABS.MEDICATION);
        break;
      case FormType.FormType_lab:
        patientFileActions.setActiveTabId(ID_TABS.LAB);
        break;
      default:
        formRulePreprocess(formItem, () =>
          musterFormDialogActions.setCurrentFormName(formItem.id)
        );
        break;
    }
  };

  const openPrintDateConfirmDialog = (formItem: Form) => {
    setIsOpen(true);
    setSelectedForm(formItem);
  };

  const handleClickAction = async (action: FormAction | undefined, formItem: Form) => {
    setFormAction(action);
    setFormId(formItem.id);

    if (formItem.id === HANDOVER_LETTER_FORM) {
      setOpenHandoverLetter(true);
      refetch();
      return;
    }

    switch (action) {
      case FormAction.FormAction_PrintFull:
        handlePrescribe(formItem);
        break;
      case FormAction.FormAction_PrintHeader:
      case FormAction.FormAction_PrintWithoutContent:
      case FormAction.FormAction_PrintWithBSNRAndLANR:
        openPrintDateConfirmDialog(formItem);
        break;
      case FormAction.FormAction_PrintOnly:
        setPrintUrl(formItem.fileNameWithVersion);
        break;
      case FormAction.FormAction_OpenNewTab: {
        const docType =
          formItem.formType === FormType.FormType_public_document
            ? 'Dokumente'
            : 'Vertragstexte';
        const fileName = `${docType}/${formItem.fileNameWithVersion}.pdf`;
        const fileUrl = await formOverviewActions.getFileUrl(fileName);
        window.open(fileUrl, '_blank');
        break;
      }
      default:
        break;
    }
  };

  const onClosePrintPreview = () => {
    setPrintUrl('');
    setFormId('');
    musterFormDialogActions.setFormRuleProcess({ id: '' });
  };

  const onPrint = async (dateTimeStamp: number) => {
    const needAddTimeline =
      selectedForm?.isHzvFav &&
      selectedForm?.actions.includes(FormAction.FormAction_PrintWithoutContent);

    await formOverviewActions.printBlankFormOrPatientHeader({
      formItem: selectedForm,
      printDate: dateTimeStamp,
      formAction: needAddTimeline
        ? FormAction.FormAction_PrintHeader
        : formAction,
      scheinId: patientFileStore.schein.activatedSchein?.scheinId,
      toastPrintSuccess: () => {
        toastPrintSuccess();
      },
    });
    setIsOpen(false);
    setFormId('');
  };

  const customListForms = useMemo(() => {
    return listForms.map((form) => {
      const formName =
        form.title || t(`Forms.${form.id}` as Paths<typeof FormI18n>);
      let formId = '';

      if (['Gruenes_Rezept', 'T-Rezept-Muster'].includes(form.id)) {
        formId = t(`Forms.${form.id}` as Paths<typeof FormI18n>);
      } else if (form.id.includes('PatientHeader')) {
        formId = '--';
      } else {
        formId = getFormIdDisplay(form.id);
      }

      const isSVFormHasAdditionalInfo = !!ADDITIONAL_INFO_URL[form.id];

      return {
        ...form,
        formName,
        formId,
        actions: isSVFormHasAdditionalInfo ? [FormAction.FormAction_PrintOnly] : form.actions,
      };
    });
  }, [listForms]);

  const formListSorted = useMemo(() => {
    return customListForms
      .filter((item) => !checkIsSVDocument(item))
      .sort((formA, formB) => {
        const formIdA = formA.formId;
        const formIdB = formB.formId;
        const [firstA] = formIdA.split(' ');
        const [firstB] = formIdB.split(' ');

        if (firstA < firstB) {
          return -1;
        }

        if (firstA > firstB) {
          return 1;
        }

        const matchA = formIdA.match(/(\d+)/g);
        const matchB = formIdB.match(/(\d+)/g);

        if (matchA && matchB) {
          return Number(matchA[0]) - Number(matchB[0]);
        }

        return formIdA.localeCompare(formIdB);
      });
  }, [customListForms]);

  const svListSorted = useMemo(() => {
    return customListForms
      .filter((item) => checkIsSVDocument(item))
      .sort((formA, formB) => {
        const formIdA = formA.formId;
        const formIdB = formB.formId;

        if (formIdB === HANDOVER_LETTER_FORM) {
          return 1;
        }

        const [firstA] = formIdA.split(' ');
        const [firstB] = formIdB.split(' ');

        if (firstA < firstB) {
          return -1;
        }

        if (firstA > firstB) {
          return 1;
        }

        const matchA = formIdA.match(/(\d+)/g);
        const matchB = formIdB.match(/(\d+)/g);

        if (matchA && matchB) {
          return Number(matchA[0]) - Number(matchB[0]);
        }

        return formIdA.localeCompare(formIdB);
      });
  }, [customListForms]);

  const listFormsFiltered = useMemo(() => {
    return [...formListSorted, ...svListSorted].filter((item) => {
      const formName = item.formName.toLowerCase();
      const formId = item.formId.toLowerCase().replaceAll(' ', '');
      const matchQuery =
        !searchQuery ||
        formName.includes(searchQuery.trim().toLowerCase()) ||
        formId.includes(searchQuery.trim().toLowerCase().replaceAll(' ', ''));

      return (
        item.formTab &&
        item.formId !== FormName.Praxisuebergabe_V1 && // remove this form, we have used custom form - LKK-Handover-Letter
        matchQuery &&
        (!isShowSVDocumentsOnly || checkIsSVDocument(item))
      );
    });
  }, [
    formListSorted,
    svListSorted,
    searchQuery,
    isShowSVDocumentsOnly,
    currentSchein,
  ]);

  const mapFormAction = useCallback(
    (formItem: Form) => {
      const {
        isPrintFull,
        isPrintWithoutContent,
        isHeaderForm,
        isOpenNewTab,
        isPrintOnly,
      } = getSettingPrintAction(formItem);
      if (isPrintFull) {
        return FormAction.FormAction_PrintFull;
      }
      if (isPrintWithoutContent) {
        return FormAction.FormAction_PrintWithoutContent;
      }
      if (isHeaderForm) {
        return FormAction.FormAction_PrintHeader;
      }
      if (isOpenNewTab && !isPrintOnly) {
        return FormAction.FormAction_OpenNewTab;
      }
      if (isPrintOnly) {
        return FormAction.FormAction_PrintOnly;
      }
    },
    [getSettingPrintAction]
  );

  useEffect(() => {
    if (
      timelineStore.prescribeFromName === FormName.Muster_1 &&
      isShowHintSlickLeaveDuration
    ) {
      setOpenConfirmSickLeaveDialog(true);
    }
  }, [timelineStore.prescribeFromName, isShowHintSlickLeaveDuration])

  useEffect(() => {
    const formId = musterFormDialogStore.actionchainForm.id;
    let formItem = listFormsFiltered.find((item) => item.id === formId);

    if (formItem?.id) {
      const action = mapFormAction(formItem);
      if (action === FormAction.FormAction_PrintOnly) {
        formItem = {
          ...formItem,
          fileNameWithVersion: `${BASE_PATH_MVZ}/data/form/${formItem.id}.pdf`,
        };
      }
      handleClickAction(action, formItem as Form);
    }
  }, [listFormsFiltered, musterFormDialogStore.actionchainForm]);

  const formSetting = useMemo(() => {
    return printSettingsStore.formsSetting.find(
      (form) => form.formId === musterFormDialogStore.currentFormName
    );
  }, [musterFormDialogStore.currentFormName]);

  const onChangeSearchInput = debounce((e) => {
    setSearchQuery((e.target as HTMLInputElement).value);
  }, 500);

  const closeM1ICDNotifyDialog = () => {
    setOpenM1Notify(false);
    musterFormDialogActions.setFormRuleProcess({ id: '' });
  };

  return (
    <div className={className}>
      <Flex gap={16} m={16}>
        <InputGroup
          className="search-input"
          placeholder={tCommonInput('search')}
          leftElement={<Svg src={SearchIconSvgURL} />}
          onChange={onChangeSearchInput}
        />
        <SVWrapper>
          {checkIsSvSchein(patientFileStore.schein.activatedSchein) && (
            <>
              <Divider />
              <Switch
                label={t('FormOverview.showSVDocumentsOnly')}
                checked={isShowSVDocumentsOnly}
                onChange={() => {
                  setShowSVDocumentsOnly(!isShowSVDocumentsOnly);
                }}
              />
            </>
          )}
        </SVWrapper>
      </Flex>
      <Table
        columns={genColumns(
          t,
          handleClickAction,
          checkIsPrivateSchein(patientFileStore.schein.activatedSchein)
        )}
        progressPending={loadingListForms}
        data={listFormsFiltered || []}
      />
      {formSetting?.type === FormTypeSetting.FORM_TAB && (
        <MusterFormDialog
          patient={patient}
          selectedContractDoctor={selectedContractDoctor}
          isOpen={!!musterFormDialogStore.currentFormName}
          onClose={() => {
            musterFormDialogActions.setProduct(undefined);
            musterFormDialogActions.setProductType(undefined);
            musterFormDialogActions.setFormRuleProcess({ id: '' });
            setFormId('');
          }}
          onActions={async (_, prescribeId) => {
            mutationHandleBgInvoicByForm.mutate({
              patientId: patient.id,
              billingId: getBgBillingByScheinId.data?.item.id as string,
              invoiceTimelineId: prescribeId,
            });
          }}
          componentActions={formOverviewActions}
        />
      )}
      <PrintDateConfirmDialog
        formAction={formAction}
        isOpen={isOpen}
        isLoading={musterFormDialogStore.isLoadingPrescribe}
        formId={formId}
        onPrint={onPrint}
        onClose={() => {
          setIsOpen(false);
          setFormId('');
        }}
      />
      <M1ICDNotifyDialog
        isOpen={isOpenM1Notify}
        onClose={closeM1ICDNotifyDialog}
        onReview={() => {
          musterFormDialogActions.clear();
          patientFileActions.setIsEditPatientProfile(true);
          closeM1ICDNotifyDialog();
          setFormId('');
        }}
      />
      {printUrl && (
        <PrintPreviewPdfDialog
          isShowCancelBtn={false}
          file={printUrl}
          onClose={onClosePrintPreview}
          formId={formId}
          titleText={
            [FormName.Muster_PTV_3, FormName.Muster_PTV_10, FormName.AOK_FA_OC_BW_Antrag_AOK_Sports_V3].includes(formId as FormName)
              ? `${getFormIdDisplay(formId)} - ${t(`Forms.${formId}` as Paths<typeof FormI18n>)}`
              : undefined
          }
          onPrintSuccess={async () => {
            const printDate = +datetimeUtil.date();
            const request: PrescribeRequest = {
              prescribe: {
                doctorId: selectedContractDoctor.doctorId || '',
                treatmentDoctorId: selectedContractDoctor.doctorId || '',
                patientId: patient.id,
                createdDate: datetimeUtil.now(),
                printedDate: printDate,
                payload: '{}',
                formName: formId as FormName,
                encounterCase: selectedContractDoctor.encounterCase || '',
                contractType: '' as ContractType,
                prescribeDate: printDate,
                scheinId: patientFileStore.schein.activatedSchein?.scheinId,
              },
              printOption: undefined,
            };

            prescribeV2(request);
          }}
        />
      )}
      <FavAlertDialog
        isOpen={openFavAlertDialog}
        hintM10={hintM10}
        handleCloseDialog={() => setOpenFavAlertDialog(false)}
        handleConfirm={() => {
          setOpenFavAlertDialog(false, () => {
            musterFormDialogActions.setCurrentFormName(selectedFormName);
          });
        }}
      />
      {isOpenHandoverLetter && (
        <HandoverLetterForm
          isOpen
          isSuccess={isSuccess}
          lkkPatients={lkkPatients}
          onClose={() => setOpenHandoverLetter(false)}
        />
      )}

      <InfoConfirmDialog
        type="primary"
        isOpen={openConfirmSickLeaveDialog}
        confirmText={tConfirmSickLeaveDialog('confirmButton')}
        cancelText={tConfirmSickLeaveDialog('cancelButton')}
        isShowIconTitle={false}
        isCloseButtonShown={false}
        onConfirm={() => {
          setOpenConfirmSickLeaveDialog(false, () => {
            if (timelineStore.prescribeFromName === FormName.Muster_1) {
              timelineActions.setPrescribeFormName(undefined);
            }

            musterFormDialogActions.setCurrentFormName(FormName.Muster_52_2_V3);
          });
        }}
        onClose={() =>
          setOpenConfirmSickLeaveDialog(false, () => {
            if (timelineStore.prescribeFromName === FormName.Muster_1) {
              timelineActions.setPrescribeFormName(undefined);
              return;
            }

            musterFormDialogActions.setCurrentFormName(FormName.Muster_1);
          })
        }
      >
        <Flex column>{tConfirmSickLeaveDialog('description')}</Flex>
      </InfoConfirmDialog>

      <InfoConfirmDialog
        type="primary"
        isOpen={openConfirmMaximumUVGoaF9990}
        title={tConfirmMaximumUVGoaF9990('title')}
        confirmText={tConfirmMaximumUVGoaF9990('confirmButton')}
        cancelText={tConfirmMaximumUVGoaF9990('cancelButton')}
        isShowIconTitle={false}
        isCloseButtonShown={false}
        onConfirm={() => {
          setOpenConfirmMaximumUVGoaF9990(false, () => {
            musterFormDialogActions.setCurrentFormName(FormName.F9990);
          });
        }}
        onClose={() => setOpenConfirmMaximumUVGoaF9990(false)}
      >
        <Flex column>{tConfirmMaximumUVGoaF9990('description')}</Flex>
      </InfoConfirmDialog>
    </div>
  );
};

export default FormOverview;
