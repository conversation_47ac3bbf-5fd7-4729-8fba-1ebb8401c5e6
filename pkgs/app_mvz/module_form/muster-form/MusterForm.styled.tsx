import { ComponentType } from 'react';

import { COLOR } from '@tutum/design-system/themes/styles';
import COLOR_GLOBAL from '@tutum/design-system/themes/styles/color/global.color';
import { FormName } from '@tutum/hermes/bff/form_common';
import Theme from '@tutum/mvz/theme';
import OriginalMusterFormMemo, { IMusterFormMemoProps } from './MusterForm';
import { getCssClass } from '@tutum/design-system/infrastructure/utils';

const styled = Theme.styled;

const MusterForm: ComponentType<IMusterFormMemoProps> = styled(
  OriginalMusterFormMemo
).attrs(({ className }) => ({
  className: getCssClass('sl-muster-form', className),
}))`
  width: 100%;
  padding: 8px;

  .sl-form {
    width: 100%;
    max-width: 1280px;
    position: relative;
    &:focus,
    &:focus-visible,
    &:focus-within {
      outline-offset: 4px;
    }

    &.${FormName.Private} {
      position: relative;

      &:before {
        content: '';
        position: absolute;
        left: 17px;
        bottom: 50px;
        width: calc(100% - 34px);
        height: 100px;
        background-color: ${COLOR_GLOBAL.blue200};
      }
    }

    &.${FormName.Muster_11}, &.${FormName.Muster_50}, &.${FormName.Muster_51} {
      .label_patient_fullname,
      .label_ik_number,
      .label_insurance_number {
        font-size: 25px;
        width: calc(100% - 5px);
        white-space: nowrap;
        overflow: hidden;
      }
    }

    &.${FormName.Muster_61} {
      .label_patient_fullname_2,
      .label_ik_number_2,
      .label_insurance_number_2,
      .label_patient_fullname_3,
      .label_ik_number_3,
      .label_insurance_number_3,
      .label_patient_fullname_4,
      .label_ik_number_4,
      .label_insurance_number_4 {
        font-size: 25px;
        width: calc(100% - 5px);
        white-space: nowrap;
        overflow: hidden;
      }
    }

    &.${FormName.Muster_12A},
      &.${FormName.Muster_N63A},
      &.${FormName.Muster_56},
      &.${FormName.Muster_64} {
      .label_patient_fullname_1,
      .label_ik_number_1,
      .label_insurance_number_1 {
        font-size: 20px;
        width: calc(100% - 5px);
        white-space: nowrap;
        overflow: hidden;
      }
    }

    &.${FormName.Muster_12A} {
      .label_patient_fullname_1,
      .label_ik_number_1,
      .label_insurance_number_1 {
        font-size: 25px;
      }
    }

    &.${FormName.Muster_11} {
      .label_patient_fullname,
      .label_ik_number,
      .label_insurance_number {
        font-size: 35px;
      }
    }

    &.${FormName.Muster_56} {
      .label_patient_fullname_1,
      .label_ik_number_1,
      .label_insurance_number_1 {
        font-size: 24px;
        width: calc(100% - 15px);
      }
    }

    &.${FormName.Muster_N63A} {
      .label_patient_fullname_1,
      .label_ik_number_1,
      .label_insurance_number_1 {
        width: calc(100% - 10px);
      }
    }

    &.${FormName.Muster_64} {
      .label_patient_fullname_1,
      .label_ik_number_1,
      .label_insurance_number_1 {
        font-size: 26px;
      }
    }

    &.${FormName.Muster_12A} {
      .sl-form-date-input .bp5-popover-target .bp5-input {
        font-size: 13px;
      }
    }

    // scale font size for form has patient header with small width
    &.${FormName.Muster_1},
      &.${FormName.Muster_6},
      &.${FormName.Muster_5},
      &.${FormName.Muster_10},
      &.${FormName.Muster_10C},
      &.${FormName.Muster_12A},
      &.${FormName.Muster_19B},
      &.${FormName.Muster_20A},
      &.${FormName.Muster_28A},
      &.${FormName.Muster_56},
      &.${FormName.Muster_52_0_V2},
      &.${FormName.Muster_52_2_V3},
      &.${FormName.Muster_N63A},
      &.${FormName.Muster_61},
      &.${FormName.Muster_64},
      &.${FormName.Muster_65A},
      &.${FormName.Muster_70},
      &.${FormName.Muster_70A},
      &.${FormName.Muster_PTV_1A},
      &.${FormName.Muster_PTV_11A},
      &.${FormName.Muster_PTV_12A},
      &.${FormName.DMP_Enrollment_Form},
      &.${FormName.Muster_39A},
      &.${FormName.Muster_22A},
      &.${FormName.Muster_26A},
      &.${FormName.Muster_27A},
      &.${FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6},
      &.${FormName.BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2},
      &.${FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5},
      &.${FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5},
      &.${FormName.AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3} {
      & .patient-header {
        .sl-form-date-input .bp5-popover-target .bp5-input,
        &.sl-form-render-annotation .react-select__control {
          font-size: 17px;
        }
      }

      & .sl-referral-tss-input {
        border: unset;
      }
    }

    &.${FormName.F1050}, 
      &.${FormName.F1000}, 
      &.${FormName.F2100}, 
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2} {
      .sl-form-date-input {
        .bp5-input-left-container {
          min-width: 16px;
          width: 16px;
        }

        .bp5-popover-target .bp5-input {
          font-size: 11px;
        }
      }

      .label_address_0,
      .label_active_insurance_name_0 {
        display: -webkit-box;
        font-size: 14px;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
        word-wrap: normal;
        line-height: normal;
        overflow: hidden;
      }
    }

    &.${FormName.F9990} {
      .sl-form-render-annotation .react-select__control {
        font-size: 16px;
      }
  
      [class*='label_uv_goa'] {
        display: -webkit-box;
        max-height: 32px;
        font-size: 14px;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
        word-wrap: normal;
        line-height: normal;
        overflow: hidden;
      }

      [class*='label_gebuhr'],
      [class*='label_besondere'] {
        width: 100%;
        text-align: center;
      }

      [class*='label_sum_eur'] {
        display: flex;
        align-self: flex-end;
        justify-content: flex-end;
        width: 100%;
      }
    }

    &.${FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2},
      &.${FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3},
      &.${FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3} {
        .sl-form-date-input {
          .bp5-input-left-container {
            min-width: 20px;
            width: 20px;
          }

          .bp5-popover-target .bp5-input {
            font-size: 14px;
          }
        }
    }

    &.${FormName.BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1} {
      .sl-form-date-input#date_label_custom_validity_of_living_will
        .bp5-popover-target
        .bp5-input {
        font-size: 10px;
      }
    }
  }

  .sl-button-take-over {
    align-self: flex-end;
  }
  &.${FormName.Muster_12A},
    &.${FormName.Muster_70},
    &.${FormName.Muster_70A},
    &.${FormName.Muster_7},
    &.${FormName.Muster_15} {
    .bp5-input {
      font-size: 20px;
    }
  }

  .sl-img {
    width: 100%;
    max-width: 1280px;
    background-color: ${COLOR.BACKGROUND_PRIMARY_WHITE};
  }

  .sl-edit-btn {
    border: 1px solid ${COLOR.BORDER_INFO};
    background-color: transparent !important;
    min-width: 60px;
    color: ${COLOR.TEXT_INFO} !important;
    border-radius: 4px;
    height: 30px;
  }

  .sl-edit-btn:hover {
    background-color: ${COLOR.BACKGROUND_HOVER} !important;
  }

  .react-select {
    &__control {
      min-height: 26px;
      padding: 0;
      font-size: 25px;
      border: 0;
      box-shadow: none;

      &:not(.react-select__control--is-disabled) {
        background-color: transparent;
      }

      &:hover {
        border: 0;
      }
    }

    &__input-container {
      margin: 0;
      padding: 0;
    }
  }
`;

export default MusterForm;
