import React, {
  memo,
  useMemo,
  useEffect,
  useState,
  useContext,
  useCallback,
} from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';

import { Flex, LoadingState, Svg } from '@tutum/design-system/components';
import { parseNumber } from '@tutum/design-system/infrastructure/utils';
import { FormName } from '@tutum/hermes/bff/form_common';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import {
  IFormField,
  IFormFieldType,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import RenderAnnotation from '@tutum/mvz/components/form-annotation/RenderAnnotation.styled';
import RenderAnnotations from '@tutum/mvz/components/form-annotation/RenderAnnotations.styled';
import {
  FORM_GROUP_BREAK_LINE_INPUT,
  FORM_SETTING_OBJECT,
  M6_HVZ_COVER_LETTER_FORM,
} from '@tutum/mvz/constant/form';
import useFocusTrap from '@tutum/mvz/hooks/useFocusTrap';
import type FormI18n from '@tutum/mvz/locales/en/Form.json';
import HimiAdditionalForm from '@tutum/mvz/module_himi/himi-additional-form/HimiAdditionalForm.styled';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IMvzThemeProps } from '@tutum/mvz/theme';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import type { LexicalEditor } from 'lexical';
import { usePatientFileStore } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import {
  musterFormDialogActions,
  useMusterFormDialogStore,
} from '../muster-form-dialog/musterFormDialog.store';
import {
  customComponentAmbulantesOperieren,
  customComponentsMuster1,
  customComponentsMuster10,
  customComponentsMuster10A,
  customComponentsMuster10C,
  customComponentsMuster12,
  customComponentsMuster13,
  customComponentsMuster15,
  customComponentsMuster16,
  customComponentsMuster28,
  customComponentsMuster2B,
  customComponentsMuster39,
  customComponentsMuster4,
  customComponentsMuster5,
  customComponentsMuster52_0,
  customComponentsMuster6,
  customComponentsMuster65,
  customComponentsMuster8,
  customComponentsMusterTransitionManagement,
  customComponentsMuster55,
  customComponentsMuster61,
  customComponentsMusterFavCoverLetter,
  customComponentsMuster63,
  customComponentsMuster64,
  customComponentsMuster56,
  customBKK_BY_HZV_Schnellinfo_Patientenbegleitung,
  customBKK_BOSCH_BW_Schnellinfo_Patientenbegleitung,
  customBKK_VAG_BW_Schnellinformation_Patientenbegleitung,
  customBKK_VAG_HE_Schnellinformation_Patientenbegleitung,
  customComponentsMusterPTV11,
  customComponentsMuster19,
  customComponentsMuster7,
  customComponentsMuster11,
  customComponentsG8EHICI,
  customComponentsMuster26,
  customComponentsMusterPTV2,
  customComponentsMusterPTV12,
  customComponentsMEDI_FA_PT_BW,
  customComponentsAOK_BW_Beratungsbogen_Einbindung_SD,
  customComponentAOK_FA_NPPP_BW_GDK_Antragsformular,
  customComponentF1050,
  customComponentF9990,
  customComponentAOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3,
} from './custom-component';
import {
  getDateFormat,
  getDateRange,
  handleClassNameItem,
  passValidateToTextField,
  preProcessFieldAnnotationList,
  parseInputReferral,
  defaultTssAddress,
} from './MusterForm.helper';
import { musterFormActions, useMusterFormStore } from './musterForm.store';
import {
  processAWH_01_Checkliste_Psychosomatik,
  processAWH_01_Kurzantrag_HZV_KinderReha,
  processMuster10AValue,
  processMuster10Value,
  processMuster10CValue,
  processMuster12Value,
  processMuster13Value,
  processMuster15Value,
  processMuster19Value,
  processMuster1Value,
  processMuster21Value,
  processMuster28Value,
  processMuster2BValue,
  processMuster36E201707Value,
  processMuster39Value,
  processMuster3Value,
  processMuster4Value,
  processMuster52Value,
  processMuster52_0Value,
  processMuster5Value,
  processMuster65Value,
  processMuster6Value,
  processMuster8Value,
  processMuster9Value,
  processMusterEmergencyPlanValue,
  processMusterTransitionManagementValue,
  processMuster61Value,
  processMuster63Value,
  processMuster56Value,
  processAWH_01ChecklisteSomatikValue,
  processBKK_BOSCH_BW_Schnellinfo_PatientenbegleitungValue,
  processMusterPTV1A,
  processMusterPTV11A,
  processG81_EHIC,
  processMuster11Value,
  processMuster50Value,
  processMuster51Value,
  processMuster22Value,
  processMuster26Value,
  processMusterPTV2AValue,
  processMusterPTV12AValue,
  processMEDI_FA_PT_BW,
  processAOK_FA_NPPP_BW_GDK_Antragsformular,
  processBKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater,
  processMusterF1050Value,
  processAOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3,
} from './process-muster';
import { useHeimiSelectionStore } from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import {
  useReferralThroughTssStore,
  referralThroughTssActions,
} from '@tutum/mvz/hooks/useReferralThroughTss.store';
import { parsingReferralCode } from '@tutum/mvz/module_kv_hzv_schein/CreateSchein.service';
import { FormEAUStylesheet } from '@tutum/mvz/components/form-eau-stylesheet';
import GlobalContext from '@tutum/mvz/contexts/Global.context';
import { FacilityType } from '@tutum/hermes/bff/bsnr_common';
import { getPatientUpdatedData } from '@tutum/mvz/module_form/module_form.util';
import CoverLetterForm from './cover-letter-form/CoverLetterForm.styled';

export const PSEUDOIK_FORM907 = '108095250';

export interface IMusterFormMemoProps {
  className?: string;
  patient?: IPatientProfile;
  currentFormSetting?: FORM_SETTING_OBJECT;
  isViewOnly: boolean;
  isRefillOnly?: boolean;
  isLabForms?: boolean;
  isStatistics?: boolean;
  formInfoMap?: { [key: string]: string };
  currentFormName?: string;
  doctorStamp?: IEmployeeProfile;
  hasSupportForm907: boolean;
  hasSupportABRD850: boolean;
  onChangeProduct: () => void;
  setValueFormReferralToInit: () => void;
  onCreateSchein?: () => void;
}

export interface FormMetadata {
  formId: string;
  pageNo: number;
  fontSizeInPx: string;
}

const MusterFormDialog = (
  props: IMusterFormMemoProps &
    IMvzThemeProps &
    II18nFixedNamespace<keyof typeof FormI18n.Form>
) => {
  const {
    className,
    patient,
    isViewOnly,
    isRefillOnly,
    isLabForms,
    isStatistics,
    currentFormName,
    formInfoMap,
    hasSupportForm907,
    hasSupportABRD850,
    t,
    setValueFormReferralToInit,
    onCreateSchein,
  } = props;

  const heimiStore = useHeimiSelectionStore();
  const [focusInput, setFocusInput] = useState(true);
  const [valueInputRefCode, setvalueInputRefCode] = useState('');
  const [valueInputRefCodeLine2, setValueInputRefCodeLine2] = useState('');
  const musterFormDialogStore = useMusterFormDialogStore();
  const { hasSetFormAnnotation } = useMusterFormStore();
  const patientFileStore = usePatientFileStore();
  const store = useMusterFormStore();
  const currentSchein = useCurrentSchein();
  const [setFocusRef] = useFocusTrap();
  const referralThroughTssStore = useReferralThroughTssStore();
  const globalContext = useContext(GlobalContext.instance);
  const doctorList = globalContext.useGetDoctorList();
  const isFormPTV11 = [FormName.Muster_PTV_11A, FormName.Muster_PTV_11B];
  const formGroupBreakLineInput: string[][] = useMemo(() => {
    let groupBreakLineInput = FORM_GROUP_BREAK_LINE_INPUT;
    if (heimiStore?.isStandardCombination) {
      groupBreakLineInput = [
        ...FORM_GROUP_BREAK_LINE_INPUT,
        [
          'textbox_heilmittel_line1_0',
          'textbox_heilmittel_line2_0',
          'textbox_heilmittel_line3_0',
        ],
      ];
    }
    if (
      currentFormName === FormName.Muster_6_cover_letter &&
      referralThroughTssStore.tssCode
    ) {
      groupBreakLineInput = [
        ...FORM_GROUP_BREAK_LINE_INPUT,
        [
          'textbox_auftrag_line2_0',
          'textbox_auftrag_line3_0',
          'textbox_auftrag_line4_0',
        ],
      ];
    } else if (
      currentFormName === FormName.Muster_6 &&
      referralThroughTssStore.tssCode
    ) {
      groupBreakLineInput = [
        ...FORM_GROUP_BREAK_LINE_INPUT,
        [
          'textbox_auftrag_line2',
          'textbox_auftrag_line3',
          'textbox_auftrag_line4',
        ],
      ];
    } else if (
      (currentFormName === FormName.Muster_PTV_11A ||
        currentFormName === FormName.Muster_PTV_11B) &&
      referralThroughTssStore.tssCode
    ) {
      groupBreakLineInput = [
        ...FORM_GROUP_BREAK_LINE_INPUT,
        ['textbox_text_line3', 'textbox_text_line4'],
      ];
    } else {
      groupBreakLineInput = [
        ...FORM_GROUP_BREAK_LINE_INPUT,
        [
          'textbox_auftrag_line1_0',
          'textbox_auftrag_line2_0',
          'textbox_auftrag_line3_0',
          'textbox_auftrag_line4_0',
        ],
        [
          'textbox_auftrag_line1',
          'textbox_auftrag_line2',
          'textbox_auftrag_line3',
          'textbox_auftrag_line4',
        ],
        [
          'textbox_text_line1',
          'textbox_text_line2',
          'textbox_text_line3',
          'textbox_text_line4',
        ],
      ];
    }

    return groupBreakLineInput;
  }, [heimiStore?.isStandardCombination, referralThroughTssStore.tssCode]);

  const isShowCoverLetterForm = useMemo(() => {
    return (
      hasSupportABRD850 &&
      musterFormDialogStore.currentFormName === FormName.Muster_6 &&
      M6_HVZ_COVER_LETTER_FORM.includes(
        musterFormDialogStore.currentFormSetting?.[
          'textbox_uberweisung'
        ] as string
      )
    );
  }, [
    hasSupportABRD850,
    musterFormDialogStore.currentFormName,
    musterFormDialogStore.currentFormSetting,
  ]);

  const [textModuleInstance, setTextModuleInstance] = useState<
    Record<string, LexicalEditor>
  >({});

  const insuranceStatus = (value: string) => {
    const currentDoctor = doctorList.find(
      (doctor) => doctor.id === musterFormDialogStore.doctor?.value
    );

    if (
      ![FormName.Muster_4].includes(
        musterFormDialogStore.currentFormName as FormName
      ) ||
      currentDoctor?.bsnrFacilityType !== FacilityType.FacilityType_Hospital
    ) {
      return value;
    }

    return value?.replace(/.$/, '6');
  };

  useEffect(() => {
    musterFormActions.setFormName(
      currentFormName || '',
      patient,
      currentSchein,
      undefined,
      isStatistics
    );
  }, [currentFormName, patient, currentSchein, isStatistics]);

  useEffect(() => {
    if (
      !isEmpty(formInfoMap) &&
      !isEmpty(musterFormDialogStore.currentFormSetting) &&
      hasSetFormAnnotation
    ) {
      const patientUpdatedData = getPatientUpdatedData(formInfoMap);
      const patientHeaderForm =
        musterFormDialogStore.isViewForm &&
        currentSchein?.scheinId !== musterFormDialogStore.formViewScheinId
          ? patientUpdatedData
          : formInfoMap;

      musterFormDialogActions.setCurrentMusterFormSetting({
        ...patientHeaderForm,
        label_insurance_status: insuranceStatus(
          formInfoMap?.['label_insurance_status'] || ''
        ),
      });
    }
  }, [
    JSON.stringify(formInfoMap),
    hasSetFormAnnotation,
    musterFormDialogStore.doctor?.value,
    musterFormDialogStore.isViewForm,
    currentSchein?.scheinId,
    musterFormDialogStore.formViewScheinId,
  ]);

  useEffect(() => {
    if (
      !musterFormDialogStore.himiPrescriptionId ||
      musterFormDialogStore.isRefill
    ) {
      musterFormDialogActions.setShowCoverLetter(isShowCoverLetterForm);
    }
  }, [
    isShowCoverLetterForm,
    musterFormDialogStore.himiPrescriptionId,
    musterFormDialogStore.isRefill,
  ]);

  useEffect(() => {
    if (referralThroughTssStore.tssCode) {
      const myTimeout = setTimeout(function () {
        setFocusInput(false);
      }, 5000);
      let input = '';
      if (isFormPTV11.includes(currentFormName as FormName)) {
        if (referralThroughTssStore.tssCode?.error) {
          input = defaultTssAddress; // default data error
        } else {
          input = `Vermittlungscode: ${parsingReferralCode(
            referralThroughTssStore.tssCode?.data?.referenceCode || ''
          )};`;
          setValueInputRefCodeLine2(defaultTssAddress);
        }
      } else {
        input =
          parseInputReferral(
            referralThroughTssStore.tssCode,
            'Vermittlungscode'
          ) || '';
      }
      setvalueInputRefCode(input);
      referralThroughTssActions.setTssCodeText(input);
      return () => {
        clearTimeout(myTimeout);
      };
    }
  }, [referralThroughTssStore.tssCode]);

  const processFormMuster = (formField: IFormField, cloned: object) => {
    switch (currentFormName) {
      case FormName.BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17:
        processMEDI_FA_PT_BW(formField, cloned);
        break;
      case FormName.AOK_FA_NPPP_BW_GDK_Antragsformular_V6:
      case FormName.BKK_BOSCH_FA_BW_GDK_Antragsformular_V4:
      case FormName.BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3:
      case FormName.BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2:
        processAOK_FA_NPPP_BW_GDK_Antragsformular(formField, cloned);
        break;
      case FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
      case FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
        processBKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater(formField, cloned);
        break;
      case FormName.Muster_15:
        processMuster15Value(formField, cloned);
        break;
      case FormName.Muster_8:
      case FormName.Muster_8A:
        processMuster8Value(formField, cloned, musterFormDialogStore);
        break;
      case FormName.Muster_1:
        processMuster1Value({
          formField,
          cloned,
          musterFormDialogStore,
          t,
          patient,
        });
        break;
      case FormName.Muster_6:
        processMuster6Value(formField, cloned, musterFormDialogStore);
        break;
      case FormName.Muster_10:
        processMuster10Value(formField, cloned, patientFileStore);
        break;
      case FormName.Muster_10A:
        processMuster10AValue(formField, cloned);
        break;
      case FormName.Muster_10C:
        processMuster10CValue(formField, cloned);
        break;
      case FormName.Muster_4:
        processMuster4Value(formField, cloned);
        break;
      case FormName.Muster_13:
        processMuster13Value(formField, cloned, musterFormDialogStore);
        break;
      case FormName.Muster_39A:
        processMuster39Value(formField, cloned);
        break;
      case FormName.Muster_2B:
        processMuster2BValue(formField, cloned);
        break;
      case FormName.Muster_52_0_V2:
      case FormName.Muster_52_2_V3:
        processMuster52_0Value({
          formField,
          cloned,
        });
        break;
      case FormName.Muster_3A:
        processMuster3Value(formField, cloned);
        break;
      case FormName.Muster_5:
        processMuster5Value(formField, cloned);
        break;
      case FormName.Muster_9:
        processMuster9Value(formField, cloned);
        break;
      case FormName.Muster_11:
        processMuster11Value(formField, cloned);
        break;
      case FormName.Muster_12A:
        processMuster12Value(formField, cloned);
        break;
      case FormName.BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1:
        processMusterEmergencyPlanValue(formField, cloned);
        break;
      case FormName.Muster_21:
        processMuster21Value(formField, cloned);
        break;
      case FormName.Muster_19A:
      case FormName.Muster_19B:
      case FormName.Muster_19C:
        processMuster19Value(formField, cloned);
        break;
      case FormName.AWH_01_Kurzantrag_HZV_KinderReha_V1:
        processAWH_01_Kurzantrag_HZV_KinderReha({ formField, cloned });
        break;
      case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
      case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
      case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
      case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
      case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3:
        processMusterTransitionManagementValue(formField, cloned);
        break;
      case FormName.Muster_26A:
        processMuster26Value(formField, cloned);
        break;
      case FormName.Muster_28A:
        processMuster28Value(formField, cloned);
        break;
      case FormName.Muster_36_E_2017_07:
        processMuster36E201707Value(formField, cloned);
        break;
      case FormName.AWH_01_Checkliste_Psychosomatik_V1:
        processAWH_01_Checkliste_Psychosomatik({ formField, cloned });
        break;
      case FormName.Muster_50:
        processMuster50Value(formField, cloned);
        break;
      case FormName.Muster_N63A:
        processMuster63Value(formField, cloned);
        break;
      case FormName.Muster_61:
        processMuster61Value(formField, cloned);
        break;
      case FormName.Muster_65A:
        processMuster65Value(formField, cloned);
        break;
      case FormName.AWH_01_Checkliste_Somatik_V1:
        processAWH_01ChecklisteSomatikValue({ formField, cloned });
        break;
      case FormName.Muster_56:
        processMuster56Value(formField, cloned);
        break;
      case FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6:
        processBKK_BOSCH_BW_Schnellinfo_PatientenbegleitungValue(
          formField,
          cloned
        );
        break;
      case FormName.Muster_PTV_1A:
        processMusterPTV1A(formField, cloned);
        break;
      case FormName.Muster_PTV_2A:
        processMusterPTV2AValue(formField, cloned);
        break;
      case FormName.Muster_PTV_11A:
        processMusterPTV11A(formField, cloned);
        break;
      case FormName.Muster_PTV_12A:
        processMusterPTV12AValue(formField, cloned);
        break;
      case FormName.G81_EHIC_Bulgarisch:
      case FormName.G81_EHIC_Danisch:
      case FormName.G81_EHIC_Englisch:
      case FormName.G81_EHIC_Franzosisch:
      case FormName.G81_EHIC_Griechisch:
      case FormName.G81_EHIC_Italienisch:
      case FormName.G81_EHIC_Kroatisch:
      case FormName.G81_EHIC_Niederlandisch:
      case FormName.G81_EHIC_Polnisch:
      case FormName.G81_EHIC_Rumanisch:
      case FormName.G81_EHIC_Spanisch:
      case FormName.G81_EHIC_Tschechisch:
      case FormName.G81_EHIC_Ungarisch:
      case FormName.G81_EHIC_Finnisch:
      case FormName.G81_EHIC_Estnisch:
      case FormName.G81_EHIC_Slowenisch:
      case FormName.G81_EHIC_Slowakisch:
      case FormName.G81_EHIC_Schwedisch:
      case FormName.G81_EHIC_Portugiesisch:
      case FormName.G81_EHIC_Litauisch:
      case FormName.G81_EHIC_Lettisch:
        processG81_EHIC(formField, cloned);
        break;
      case FormName.Muster_51:
        processMuster51Value(formField, cloned);
        break;
      case FormName.Muster_22A:
        processMuster22Value(formField, cloned);
        break;
      case FormName.F1050:
      case FormName.F1000:
      case FormName.F2100:
      case FormName.F9990:
        processMusterF1050Value(formField, cloned, currentFormName);
        break;
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3:
        processAOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3(
          formField,
          cloned
        );
        break;
      default:
        break;
    }
  };

  const internalOnChangeForm = (
    currentFormSetting: FORM_SETTING_OBJECT,
    formField: IFormField,
    newVal?: string | number | boolean
  ) => {
    const cloned = { ...currentFormSetting };
    if (formField.type === IFormFieldType.DATE_PICKER) {
      if (formField.name.includes('date_prescribe')) {
        musterFormDialogActions.setPrescriptionDate(parseNumber(newVal));
      }
      cloned[formField.name] = parseNumber(newVal);
    } else if (
      formField.name.startsWith('checkbox') ||
      formField.name.startsWith('toggle')
    ) {
      cloned[formField.name] =
        !musterFormDialogStore?.currentFormSetting?.[formField?.name];
    } else {
      cloned[formField?.name] = newVal || '';
    }
    processFormMuster(formField, cloned);
    return cloned;
  };

  const onChangeFormEvent = (
    formField: IFormField,
    newVal?: string | number | boolean
  ) => {
    const cloned = internalOnChangeForm(
      musterFormDialogStore?.currentFormSetting || {},
      formField,
      newVal
    );

    musterFormDialogActions.setCurrentMusterFormSetting(cloned);
  };

  const onChangeBsnr = (data: Record<string, number | string>) => {
    musterFormDialogActions.setCurrentMusterFormSetting(data);
  };

  const onChangeFieldGroupEvent = (
    fieldValues: Array<{
      formField: IFormField;
      newVal?: string | number | boolean;
    }>
  ) => {
    let cloned = {};
    fieldValues?.forEach((fieldValue) => {
      const { formField, newVal } = fieldValue;
      cloned = internalOnChangeForm(cloned, formField, newVal);
    });
    musterFormDialogActions.setCurrentMusterFormSetting(cloned);
  };

  const renderFormCustomComponents = (
    currentFormName: string | undefined,
    formField: IFormField
  ) => {
    switch (currentFormName) {
      case FormName.Muster_16:
      case FormName.Private:
        return customComponentsMuster16(musterFormDialogStore);
      case FormName.Muster_15:
        return customComponentsMuster15(formField, musterFormDialogStore);
      case FormName.Muster_8:
      case FormName.Muster_8A:
        return customComponentsMuster8(formField, musterFormDialogStore);
      case FormName.Muster_1:
        return customComponentsMuster1(formField, musterFormDialogStore);
      case FormName.Muster_5:
        return customComponentsMuster5(formField, musterFormDialogStore);
      case FormName.Muster_6:
        return customComponentsMuster6(
          formField,
          musterFormDialogActions,
          musterFormDialogStore,
          onChangeFormEvent,
          focusInput,
          valueInputRefCode,
          setvalueInputRefCode,
          setValueFormReferralToInit
        );
      case FormName.Muster_10:
        return customComponentsMuster10(
          formField,
          store,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.Muster_10A:
        return customComponentsMuster10A(
          formField,
          store,
          musterFormDialogStore,
          onChangeFormEvent,
          isViewOnly
        );
      case FormName.Muster_10C:
        return customComponentsMuster10C(
          formField,
          store,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.Muster_12A:
        return customComponentsMuster12(formField, musterFormDialogStore);
      case FormName.Muster_13:
        return customComponentsMuster13(formField, heimiStore);
      case FormName.Muster_39A:
        return customComponentsMuster39(
          formField,
          store,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.Muster_4:
        return customComponentsMuster4(formField, musterFormDialogStore);
      case FormName.Muster_2B:
        return customComponentsMuster2B(formField, musterFormDialogStore);
      case FormName.Muster_28A:
        return customComponentsMuster28(formField, musterFormDialogStore);
      case FormName.Ambulantes_Operieren_V1:
        return customComponentAmbulantesOperieren(
          formField,
          musterFormDialogStore
        );
      case FormName.Muster_52_0_V2:
      case FormName.Muster_52_2_V3:
        return customComponentsMuster52_0(formField, musterFormDialogStore);
      case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
      case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
      case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
      case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
      case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3:
        return customComponentsMusterTransitionManagement(
          formField,
          musterFormDialogStore
        );
      case FormName.Muster_55:
        return customComponentsMuster55(formField, musterFormDialogStore);
      case FormName.Begleitschreiben_FaV_V4:
        return customComponentsMusterFavCoverLetter(
          formField,
          musterFormDialogStore
        );
      case FormName.Muster_N63A:
        return customComponentsMuster63(formField, musterFormDialogStore);
      case FormName.Muster_61:
        return customComponentsMuster61(formField, musterFormDialogStore);
      case FormName.Muster_65A:
        return customComponentsMuster65(formField, musterFormDialogStore);
      case FormName.Muster_64:
        return customComponentsMuster64(formField, musterFormDialogStore);
      case FormName.Muster_56:
        return customComponentsMuster56(formField, musterFormDialogStore);
      case FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6:
        return customBKK_BOSCH_BW_Schnellinfo_Patientenbegleitung(
          formField,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.AOK_BW_Beratungsbogen_Einbindung_SD_V7:
      case FormName.BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10:
        return customComponentsAOK_BW_Beratungsbogen_Einbindung_SD(
          formField,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6:
        return customBKK_BY_HZV_Schnellinfo_Patientenbegleitung(
          t,
          formField,
          musterFormDialogStore,
          onChangeFormEvent
        );

      case FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4:
        return customBKK_VAG_BW_Schnellinformation_Patientenbegleitung(
          formField,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1:
        return customBKK_VAG_HE_Schnellinformation_Patientenbegleitung(
          formField,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.Muster_PTV_2A:
        return customComponentsMusterPTV2(formField, musterFormDialogStore);
      case FormName.Muster_PTV_11A:
        return customComponentsMusterPTV11(
          formField,
          musterFormDialogStore,
          focusInput,
          onChangeFormEvent,
          valueInputRefCode,
          valueInputRefCodeLine2,
          setvalueInputRefCode,
          setValueInputRefCodeLine2,
          setValueFormReferralToInit
        );
      case FormName.Muster_19B:
        return customComponentsMuster19(formField, musterFormDialogStore);
      case FormName.Muster_7:
        return customComponentsMuster7(formField, musterFormDialogStore);
      case FormName.Muster_11:
        return customComponentsMuster11(formField, musterFormDialogStore);
      case FormName.G81_EHIC_Bulgarisch:
      case FormName.G81_EHIC_Danisch:
      case FormName.G81_EHIC_Englisch:
      case FormName.G81_EHIC_Franzosisch:
      case FormName.G81_EHIC_Griechisch:
      case FormName.G81_EHIC_Italienisch:
      case FormName.G81_EHIC_Kroatisch:
      case FormName.G81_EHIC_Niederlandisch:
      case FormName.G81_EHIC_Polnisch:
      case FormName.G81_EHIC_Rumanisch:
      case FormName.G81_EHIC_Spanisch:
      case FormName.G81_EHIC_Tschechisch:
      case FormName.G81_EHIC_Ungarisch:
      case FormName.G81_EHIC_Finnisch:
      case FormName.G81_EHIC_Estnisch:
      case FormName.G81_EHIC_Slowenisch:
      case FormName.G81_EHIC_Slowakisch:
      case FormName.G81_EHIC_Schwedisch:
      case FormName.G81_EHIC_Portugiesisch:
      case FormName.G81_EHIC_Litauisch:
      case FormName.G81_EHIC_Lettisch:
        return customComponentsG8EHICI(musterFormDialogStore);
      case FormName.Muster_26A:
        return customComponentsMuster26(formField, musterFormDialogStore);
      case FormName.Muster_PTV_12A:
        return customComponentsMusterPTV12(formField, musterFormDialogStore);
      case FormName.BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17:
        return customComponentsMEDI_FA_PT_BW(
          formField,
          musterFormDialogStore,
          onCreateSchein
        );
      case FormName.AOK_FA_NPPP_BW_GDK_Antragsformular_V6:
      case FormName.BKK_BOSCH_FA_BW_GDK_Antragsformular_V4:
      case FormName.BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3:
      case FormName.BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2:
      case FormName.AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3:
      case FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
      case FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
        return customComponentAOK_FA_NPPP_BW_GDK_Antragsformular(
          formField,
          musterFormDialogStore
        );
      case FormName.F1050:
      case FormName.F1000:
      case FormName.F2100:
        return customComponentF1050(
          formField,
          musterFormDialogActions,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.F9990:
        return customComponentF9990(
          formField,
          musterFormDialogStore,
          onChangeFormEvent
        );
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3:
        return customComponentAOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3(
          formField,
          musterFormDialogActions,
          musterFormDialogStore
        );
      default:
        return undefined;
    }
  };

  const handleLexical = useCallback((editor, fieldName) => {
    setTextModuleInstance((prev) => ({ ...prev, [fieldName]: editor }));
  }, []);

  if (store.isLoading) {
    return <LoadingState />;
  }

  const groupFieldBySameGroup = function (xs: IFormField[]) {
    let groupIdx = 0;
    const prevGroupName = {};

    return xs.reduce<{ [key: number]: IFormField[] }>(function (rv, x) {
      const groupInputArray = formGroupBreakLineInput.find((items) => {
        const currentElement = items.find(
          (elementName) => elementName === x.name
        );
        if (currentElement) return items;
        return undefined;
      });
      let key = groupIdx;
      if (!groupInputArray) {
        groupIdx++;
        key = groupIdx;
      } else {
        const keyCompare = groupInputArray.join('-');

        if (!prevGroupName[keyCompare]) {
          groupIdx++;
          key = groupIdx;
          prevGroupName[keyCompare] = groupIdx;
        } else {
          key = prevGroupName[keyCompare];
        }
      }
      const group = rv[key] || [];
      group.push(x);
      rv[key] = group;
      return rv;
    }, {});
  };

  const shouldRenderField = (formField, page): boolean => {
    if (formField === null) {
      return false;
    }
    const pageStr = `_${page}`;
    if (
      !formField.name.endsWith(pageStr) &&
      (store.formSVGs || []).length > 1
    ) {
      return false;
    }
    return true;
  };

  const renderFieldAnnotation = (f: any, page: number) => {
    const fieldList =
      preProcessFieldAnnotationList(
        store.formAnnotationInfo?.meta?.fields,
        musterFormDialogStore
      ) || [];

    const components: JSX.Element[] = [];
    const groups = groupFieldBySameGroup(fieldList);
    const _formId = `img-form-${f.svg}`;

    for (const fieldIdx in groups) {
      const formFields = groups[fieldIdx];

      if (formFields.length === 1) {
        const formField = formFields[0];
        if (!shouldRenderField(formField, page)) {
          continue;
        }
        const formFieldMapped = passValidateToTextField(
          formField,
          musterFormDialogStore,
          heimiStore?.currentGroup?.isBlankForm
        );
        const insuranceStatusValue = insuranceStatus(
          formInfoMap?.['label_insurance_status'] || ''
        );
        const data = {
          ...musterFormDialogStore.currentFormSetting,
          date_prescribe:
            musterFormDialogStore.currentFormSetting?.['date_prescribe'] ||
            musterFormDialogStore.prescriptionDate ||
            0,
          label_ik_number:
            hasSupportForm907 &&
            musterFormDialogStore.currentFormName === FormName.Muster_16
              ? PSEUDOIK_FORM907
              : musterFormDialogStore.currentFormSetting?.['label_ik_number'] ||
                '',
        };

        const comp = (
          <RenderAnnotation
            formField={formFieldMapped}
            key={formField.name}
            formImgId={_formId}
            onChangeEvent={onChangeFormEvent}
            page={page}
            insuranceStatusValue={insuranceStatusValue}
            onChangeBsnr={onChangeBsnr}
            fieldIndex={fieldIdx as unknown as number}
            labelFontSize={store.labelfontSize}
            textFontSize={store.textFontSize}
            formMetaData={store.formAnnotationInfo}
            dateFormat={getDateFormat(formField, musterFormDialogStore)}
            componentValue={get(data, formField.name)}
            prescribeDate={musterFormDialogStore.prescriptionDate}
            isViewOnly={isViewOnly}
            isRefillOnly={isRefillOnly}
            customComponents={renderFormCustomComponents(
              musterFormDialogStore.currentFormName,
              formField
            )}
            formInfoMap={data}
            classNameItem={handleClassNameItem(
              formField,
              isLabForms ? isLabForms : false
            )}
            doctorStamp={props.doctorStamp}
            dateRange={getDateRange(formField, musterFormDialogStore)}
            currentFormName={musterFormDialogStore.currentFormName}
            musterFormDialogStore={musterFormDialogStore}
            handleLexical={handleLexical}
            textModuleInstance={textModuleInstance}
            formGroupBreakLineInput={formGroupBreakLineInput}
          />
        );
        components.push(comp);
      } else {
        // render annotation for field groups
        const formField = formFields[0];
        if (!shouldRenderField(formField, page)) {
          continue;
        }
        const formFieldsMapped = formFields.map((formField) =>
          passValidateToTextField(
            formField,
            musterFormDialogStore,
            heimiStore?.currentGroup?.isBlankForm
          )
        );
        const componentValues = formFields.map((f) => {
          return get(musterFormDialogStore.currentFormSetting, f.name);
        });
        const comp = (
          <RenderAnnotations
            formFields={formFieldsMapped}
            key={formFields.map((f) => f.name).join('--')}
            formImgId={_formId}
            onChangeEvent={onChangeFieldGroupEvent}
            page={page}
            labelFontSize={store.labelfontSize}
            textFontSize={store.textFontSize}
            formMetaData={store.formAnnotationInfo}
            componentValues={componentValues}
            prescribeDate={musterFormDialogStore.prescriptionDate}
            currentFormName={musterFormDialogStore.currentFormName}
            musterFormDialogStore={musterFormDialogStore}
            isViewOnly={isViewOnly}
          />
        );
        components.push(comp);
      }
    }
    return components;
  };

  const renderForms = () => {
    if (
      musterFormDialogStore.currentFormName === FormName.Muster_1 &&
      musterFormDialogStore.isViewForm &&
      musterFormDialogStore.formPrescription
    ) {
      const { formPrescription } = musterFormDialogStore;
      return (
        <FormEAUStylesheet
          sentBundleUrl={formPrescription.eAUBundleUrl}
          cancellationBundleUrl={formPrescription.eAUCancellationBundleUrl}
          status={formPrescription.eAUStatus}
        />
      );
    }

    return store?.formSVGs?.map((f, i) => {
      const key = `medication-form-${musterFormDialogStore.currentFormName}_${f.svg}`;

      return (
        <div
          key={key}
          id={key}
          className={`sl-form ${musterFormDialogStore.currentFormName}`}
        >
          <Svg
            id={`img-form-${f.svg}`}
            src={f.svg}
            className="sl-img"
            onLoad={() => {
              musterFormActions.setImageLoaded(true, f.svg);
            }}
          />
          {!!store.formAnnotationInfo?.meta?.fields?.length &&
            f.isLoadedImage &&
            renderFieldAnnotation(
              { svg: f.svg, isLoadedImage: f.isLoadedImage },
              i
            )}
        </div>
      );
    });
  };

  return (
    <Flex align="center" column className={className} ref={setFocusRef}>
      {!musterFormDialogStore.isControllable && renderForms()}
      <div className="sl-form">
        <HimiAdditionalForm
          questionName={musterFormDialogStore.questionName}
          store={store}
          musterFormDialogStore={musterFormDialogStore}
        />
        {musterFormDialogStore.isShowCoverLetterForm && (
          <CoverLetterForm
            formName={FormName.Begleitschreiben_FaV_V4}
            store={store}
            musterFormDialogStore={musterFormDialogStore}
          />
        )}
      </div>
    </Flex>
  );
};

export default memo(
  I18n.withTranslation(MusterFormDialog, {
    namespace: 'Form',
    nestedTrans: 'Form',
  })
);
