import {
  ICustomAnnotationComponent,
  IFormField,
} from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import React from 'react';
import {
  IMusterPrescribe,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { ReactSelect } from '@tutum/design-system/components';
import TimeFormInput from '@tutum/mvz/components/form-annotation/time-input/TimeFormInput.styled';

const Auswahl_Options = [
  {
    label: 'Auswahl',
    value: 'Auswahl',
  },
  {
    label: 'ambulante/stationäre',
    value: 'ambulante/stationäre',
  },
  {
    label: 'ambulante',
    value: 'ambulante',
  },
  {
    label: 'stationäre',
    value: 'stationäre',
  },
];

export default (
  formField: IFormField,
  musterFormDialogStore: IMusterPrescribe,
  onChangeFormEvent: (
    formField: IFormField,
    newVal?: string | number | boolean
  ) => void
) => {
  let components: ICustomAnnotationComponent[] = [];

  if (
    [
      'label_time_arrive_time_0',
      'label_time_accident_time_0',
      'label_time_start_time_0',
      'label_time_end_time_0',
    ].includes(formField.name)
  ) {
    const fieldValue =
      musterFormDialogStore?.currentFormSetting?.[formField.name] || 0;
    const component: ICustomAnnotationComponent = {
      fieldName: formField.name,
      component: () => (
        <TimeFormInput
          field={formField}
          isViewOnly={musterFormDialogStore.isViewForm}
          isSetCurrentTime={false}
          defaultDate={+fieldValue}
          onChange={onChangeFormEvent}
        />
      ),
    };
    components = [...components, component];
  }

  if (formField.name === 'label_rechnung') {
    const component: ICustomAnnotationComponent = {
      fieldName: formField.name,
      component: () => (
        <ReactSelect
          items={Auswahl_Options}
          selectedValue={
            musterFormDialogStore.currentFormSetting?.[
            'label_rechnung'
            ] as string
        }
          isDisabled={musterFormDialogStore.isViewForm}
          onItemSelect={(item) => {
            onChangeFormEvent(formField, item.value);
          }}
        />
      ),
    };
    components = [...components, component];
  }

  return components;
};
