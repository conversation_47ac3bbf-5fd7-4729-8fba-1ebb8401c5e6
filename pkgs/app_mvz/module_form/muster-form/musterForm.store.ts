import { useEffect } from 'react';
import { proxy, useSnapshot } from 'valtio';

import { getAge } from '@tutum/design-system/infrastructure/utils';
import { EHIC_PRF_NR, KBV_PRF_NR } from '@tutum/hermes/bff/app_mvz_form';
import { FormName } from '@tutum/hermes/bff/form_common';
import {
  Gender,
  InsuranceInfo,
  PatientType,
  TypeOfInsurance,
} from '@tutum/hermes/bff/patient_profile_common';
import { MainGroup, ScheinItem } from '@tutum/hermes/bff/schein_common';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { getActiveInsurance } from '@tutum/mvz/_utils/checkInsurance';
import { getDateOfBirth } from '@tutum/mvz/_utils/formatBirthday';
import { IFormInfo } from '@tutum/mvz/components/form-annotation/FormAnnotation.type';
import {
  M6_FAV_PRE_DEFINED_TEXT,
  M6_HVZ_PRE_DEFINED_TEXT,
} from '@tutum/mvz/constant/form';
import {
  FormTypeSetting,
  printSettingStore as printSettingsStore,
} from '@tutum/mvz/hooks/usePrintSettings.store';
import {
  musterFormDialogActions,
  musterFormDialogStore,
} from '@tutum/mvz/module_form/muster-form-dialog/musterFormDialog.store';
import { MUSTER_56_FIELD_NAMES } from '@tutum/mvz/module_form/muster-form/process-muster/muster56/constant';
import {
  IHeimiSelectionActions,
  heimiSelectionActions,
} from '@tutum/mvz/module_heimi/heimi-selection/HeimiSelection.store';
import {
  patientFileActions,
  patientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { IPatientProfile } from '@tutum/mvz/module_patient-management/types/profile.type';
import { IEmployeeProfile } from '@tutum/mvz/types/profile.type';
import {
  checkCopaymentExemptionTillDate,
  getTimelineServiceEntries,
  MAXIMUM_UV_GOA_SERVICE_F9990,
} from './MusterForm.helper';
import { store as patientEnrollmentFormStore } from '@tutum/mvz/module_patient-management/patient-enrollment/patient-enrollment-form/PatientEnrollmentForm.store';
import { fetchWithHeaders } from '@tutum/hermes/bff/legacy/api_client';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { store as storeTimeline } from '@tutum/mvz/module_patient-management/patient-file/timeline/Timeline.store';
import {
  TimelineEntityType,
  TimelineModel,
} from '@tutum/hermes/bff/timeline_common';
import { DATE_FORMAT } from '@tutum/infrastructure/shared/date-format';
import { transformPrice } from '@tutum/infrastructure/shared/price-format';
import { GroupByQuarter } from '@tutum/hermes/bff/app_mvz_timeline';
import webWorkerServices from '@tutum/infrastructure/web-worker-services';

export interface IMusterFormProps {
  formName?: string;
  formSVGs?: Array<{
    svg: string;
    isLoadedImage: boolean;
  }>;
  labelfontSize?: string;
  formAnnotationInfo?: IFormInfo;
  formValue?: {
    [key: string]: boolean | string | number;
  };
  formLabelInfo?: {
    [key: string]: string;
  };
  isLoading: boolean;
  hasSetFormAnnotation?: boolean;
  textFontSize?: string;
}

export let musterFormStore = proxy<IMusterFormProps>({
  isLoading: true,
  formValue: {},
});

// devtools(musterFormStore, 'musterFormStore');

export const musterFormActions = {
  setLoading: (isLoading: boolean) => {
    musterFormStore.isLoading = isLoading;
  },
  setFormDefaultLabelValue: (formLabelInfo) => {
    if (
      !formLabelInfo ||
      !Object.keys(formLabelInfo).length ||
      musterFormStore?.formSVGs?.length === 1
    ) {
      musterFormStore.formLabelInfo = {
        ...musterFormStore.formLabelInfo,
        ...formLabelInfo,
      };
      return;
    }
    const newMap = Object.keys(formLabelInfo).reduce((acc, cur) => {
      const newKey = `${cur}_0`;
      acc[newKey] = formLabelInfo[cur];
      return acc;
    }, {});
    musterFormStore.formLabelInfo = {
      ...musterFormStore.formLabelInfo,
      ...newMap,
    };
  },

  setFormConditionValue: () => {
    switch (musterFormStore.formName) {
      case FormName.Muster_8:
      case FormName.Muster_8A: {
        if (musterFormDialogStore.currentFormSetting) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            checkbox_erstverordnung: !musterFormDialogStore.isRefill,
            checkbox_folgeverordnung: musterFormDialogStore.isRefill,
          });
        }
        break;
      }
    }
  },

  setFormLabelValue: (formLabelInfo) => {
    musterFormStore.formLabelInfo = {
      ...musterFormStore.formLabelInfo,
      ...formLabelInfo,
    };
  },
  setImageLoaded: (isLoading: boolean, svg: string) => {
    musterFormStore.formSVGs = (musterFormStore.formSVGs || []).map((f) =>
      f.svg === svg ? { ...f, isLoadedImage: isLoading } : f
    );
  },

  setHasSetFormAnnotation: (hasSetFormAnnotation: boolean) => {
    musterFormStore.hasSetFormAnnotation = hasSetFormAnnotation;
  },

  setFormAnnotation: async (url: string, callback?: () => void) => {
    const result = await fetchWithHeaders<IFormInfo>('GET', url);
    musterFormStore.formAnnotationInfo = result.data;
    const formDefaultValue =
      musterFormStore?.formAnnotationInfo?.meta?.fields?.reduce((acc, cur) => {
        switch (cur.type) {
          case 'TOGGLE_NUMBER':
          case 'CHECK_BOX':
            acc[cur.name] = false;
            break;
          case 'TEXT':
          case 'TEXT_BOX':
          case 'LABEL':
          case 'DATE_PICKER':
          case 'AREA_TEXT_BOX':
            acc[cur.name] = '';
        }
        return acc;
      }, {});
    musterFormStore.hasSetFormAnnotation = true;
    musterFormDialogActions.setComponentValueOnInit(formDefaultValue, callback);
    musterFormActions.setFormConditionValue();
  },

  getFormSetting: async (formName?: string) => {
    const data = printSettingsStore.formsSetting.find(
      (form) => form.formId === formName
    );

    if (musterFormStore.formName !== formName) {
      musterFormStore.formName = formName;
      musterFormStore.formSVGs = (data?.svgs || []).map((s) => ({
        svg: s,
        isLoadedImage: false,
      }));
      musterFormStore.labelfontSize = data?.labelFontSize;
      musterFormStore.textFontSize = data?.textFontSize;
    }

    const result = await fetchWithHeaders<IFormInfo>(
      'GET',
      data?.annotation || ''
    );

    musterFormStore.formAnnotationInfo = result.data;
    const formDefaultValue =
      musterFormStore?.formAnnotationInfo?.meta?.fields?.reduce((acc, cur) => {
        switch (cur.type) {
          case 'TOGGLE_NUMBER':
          case 'CHECK_BOX':
            acc[cur.name] = false;
            break;
          case 'TEXT_BOX':
          case 'LABEL':
          case 'DATE_PICKER':
          case 'AREA_TEXT_BOX':
            acc[cur.name] = '';
        }
        return acc;
      }, {});
    return formDefaultValue;
  },

  setFormName: async (
    formName: string,
    patient?: IPatientProfile,
    currentSchein?: ScheinItem,
    callback?: () => void,
    isStatistics?: boolean,
    timelineState?: GroupByQuarter[]
  ) => {
    if (!formName) {
      return;
    }

    const isCreateForm =
      !isStatistics &&
      !musterFormDialogStore.isRefill &&
      !musterFormDialogStore.isViewForm &&
      !musterFormDialogStore.formPrescription?.id;
    const gender =
      patient &&
      [Gender.D, Gender.M, Gender.W, Gender.X].includes(
        patient.patientInfo.personalInfo.gender
      )
        ? patient.patientInfo.personalInfo.gender
        : '';
    const quartal = datetimeUtil
      .getStartOfBySelectedQuarter(
        currentSchein?.g4101Quarter || 0,
        currentSchein?.g4101Year || 0
      )
      .valueOf();

    const data = printSettingsStore.formsSetting.find(
      (form) => form.formId === formName
    );
    const insuranceActive = getActiveInsurance(
      patient?.patientInfo?.insuranceInfos
    );
    const addressContent = `${insuranceActive?.insuranceCompanyName}\n${[
      insuranceActive?.address?.street || '',
      insuranceActive?.address?.number || '',
    ]
      .filter(Boolean)
      .join(',')}\n${insuranceActive?.address?.postCode || ''} ${
      insuranceActive?.address?.city || ''
    }`;
    if (musterFormStore.formName != formName) {
      musterFormStore.formName = formName;
      musterFormStore.formSVGs = (data?.svgs || []).map((s) => ({
        svg: s,
        isLoadedImage: false,
      }));
      musterFormStore.labelfontSize = data?.labelFontSize;
      musterFormStore.textFontSize = data?.textFontSize;
    }

    if (!musterFormStore.hasSetFormAnnotation) {
      await musterFormActions.setFormAnnotation(data?.annotation || '');
    }
    musterFormStore.isLoading = false;
    // Keep old prf number in case view form
    if (isCreateForm || musterFormDialogStore.isRefill) {
      musterFormDialogActions.setCurrentMusterFormSetting({
        [KBV_PRF_NR.KBV_PRF_LABEL]: KBV_PRF_NR.KBV_PRF_VALUE,
        [EHIC_PRF_NR.EHIC_PRF_LABEL]: EHIC_PRF_NR.EHIC_PRF_VALUE,
      });
    }

    const formSetting = printSettingsStore.formsSetting.find(
      (form) => form.formId === formName
    );

    const patientAge = patient?.dateOfBirth
      ? getAge(new Date(patient.dateOfBirth))
      : -1;

    const selectedInsurance = patientFileStore.schein.insuranceInfos.find(
      (insurance) =>
        insurance.id === musterFormDialogStore.currentSchein?.insuranceId
    );

    const currentDoctor =
      (musterFormDialogStore.contractDoctor?.availableDoctor || []).find(
        (item) => item.id === musterFormDialogStore.doctor?.data?.id
      ) || musterFormDialogStore.contractDoctor?.availableDoctor?.[0];

    if (isCreateForm && formSetting?.type === FormTypeSetting.ENROLLMENT_FORM) {
      const teId = patientEnrollmentFormStore.currentForm?.teId || '';

      musterFormDialogActions.setCurrentMusterFormSetting({
        label_te_id_0: teId,
        label_te_id_1: teId,
        label_te_id_2: teId,
        label_te_id_3: teId,
        label_te_id_4: teId,
        label_te_id_5: teId,
      });
    }

    if (currentSchein) {
      musterFormDialogActions.setCurrentSchein(currentSchein);
    }

    switch (formName) {
      case FormName.Muster_16: {
        if (isCreateForm) {
          const insuranceActive = getActiveInsurance(
            patient?.patientInfo.insuranceInfos
          );

          if (insuranceActive?.haveCoPaymentExemptionTill) {
            const fieldNameActive = checkCopaymentExemptionTillDate(
              insuranceActive.copaymentExemptionTillDate
            )
              ? 'checkbox_gebuhrfrei'
              : 'checkbox_gebpfl';
            musterFormDialogActions.setCurrentMusterFormSetting({
              [fieldNameActive]: true,
            });
          } else {
            musterFormDialogActions.setCurrentMusterFormSetting({
              checkbox_gebpfl: true,
            });
          }

          if (patientAge !== -1 && patientAge < 18) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              checkbox_gebuhrfrei: true,
            });
          }

          musterFormDialogActions.setCurrentMusterFormSetting({
            label_fax_number: selectedInsurance?.fax
              ? `Fax: ${selectedInsurance?.fax}`
              : '',
            label_tel_number: selectedInsurance?.tel
              ? `Tel: ${selectedInsurance?.tel}`
              : '',
            textbox_fax: selectedInsurance?.fax || '',
            textbox_tel: selectedInsurance?.tel || '',
            date_label_custom_datum: +datetimeUtil.date(),
            textbox_hilfsmitte:
              musterFormDialogStore.himiDeviceName?.split('-')[0],
          });
        }

        const nachName = [
          patient?.lastName,
          patient?.firstName,
          getDateOfBirth(patient?.patientInfo.personalInfo.dateOfBirth).value,
          patient?.patientInfo.addressInfo.address.postCode,
          selectedInsurance?.insuranceNumber,
        ]
          .filter((value) => value)
          .join(', ');
        musterFormDialogActions.setCurrentMusterFormSetting({
          toggle_7_hilfsmittel: true,
          label_nachname: nachName,
        });
        break;
      }
      case FormName.Muster_8A:
      case FormName.Muster_8: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            textbox_himi_device_name: `1x ${musterFormDialogStore.himiDeviceName}`,
            textbox_ik:
              musterFormDialogStore.currentFormSetting?.['label_ik_number'],
          });
        }
        // We need to force checkbox_folgeverordnung_0 == true if we are in refill mode
        // https://www.notion.so/silenteer/Allow-to-fill-out-form-Mu-8-8a-so-that-I-can-prescribe-visual-aids-db9aa9ab42634db58b0b6f329d1c93aa
        if (musterFormDialogStore.isRefill) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            checkbox_erstverordnung: !musterFormDialogStore.isRefill,
            checkbox_folgeverordnung: musterFormDialogStore.isRefill,
          });
        }
        break;
      }
      case FormName.Muster_1: {
        if (musterFormDialogStore.isRefill) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            checkbox_erstbes_0: false,
            checkbox_folgebes_0: true,
          });
          musterFormDialogActions.setCurrentMusterFormSetting({
            label_date_festgestellt_0:
              musterFormDialogStore.currentFormSetting?.['date_festgestellt_0'],
          });
        } else if (musterFormDialogStore.isViewForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            label_date_arbeit_0:
              musterFormDialogStore.currentFormSetting?.['date_arbeit_0'],
            label_date_vorau_0:
              musterFormDialogStore.currentFormSetting?.['date_vorau_0'],
            label_date_festgestellt_0:
              musterFormDialogStore.currentFormSetting?.['date_festgestellt_0'],
          });
        } else if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            checkbox_erstbes_0: true,
            checkbox_folgebes_0: false,
            date_arbeit_0: datetimeUtil.now(),
            date_festgestellt_0: datetimeUtil.now(),
          });
        }
        break;
      }
      case FormName.Muster_5:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_m: gender,
              date_quartal: quartal,
            });
          } else if (musterFormDialogStore.isViewForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_date_quartal:
                musterFormDialogStore.currentFormSetting?.['date_quartal'],
            });
          }
        }
        break;
      case FormName.Muster_15:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              textbox_ik_0:
                musterFormDialogStore.currentFormSetting?.['label_ik_number'],
            });
          }
        }
        break;
      case FormName.Muster_19A:
      case FormName.Muster_19B:
      case FormName.Muster_19C:
        {
          if (musterFormDialogStore.isViewForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_date_quartal:
                musterFormDialogStore.currentFormSetting?.['date_quartal'],
            });
          } else if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_m: gender,
              date_quartal: quartal,
            });
          }
        }
        break;
      case FormName.Muster_6:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              date_quartal: quartal,
              date_label_full_date: +datetimeUtil.date(),
            });
          } else if (musterFormDialogStore.isViewForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_date_quartal:
                musterFormDialogStore.currentFormSetting?.['date_quartal'],
            });
          }
          musterFormDialogActions.setCurrentMusterFormSetting({
            label_geschlecht: gender,
          });
        }
        break;
      case FormName.Muster_10:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              textbox_betri: currentDoctor?.bsnr,
              textbox_arzt: currentDoctor?.lanr,
              label_gender: gender,
              date_label_custom_abnahmedatum: datetimeUtil.now(),
              date_quartal: quartal,
            });
          }

          if (musterFormDialogStore.isViewForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_date_quartal:
                musterFormDialogStore.currentFormSetting?.['date_quartal'],
              label_date_abnahmezeit:
                musterFormDialogStore.currentFormSetting?.['date_abnahmezeit'],
            });
          }
        }
        break;

      case FormName.Muster_10A:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_gender: gender,
              date_label_custom_abnahmedatum: datetimeUtil.now(),
            });
          }

          if (musterFormDialogStore.isViewForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_textbox_knappschafts_kennziffer:
                musterFormDialogStore.currentFormSetting?.[
                  'textbox_knappschafts_kennziffer'
                ],
              label_date_abnahmezeit:
                musterFormDialogStore.currentFormSetting?.['date_abnahmezeit'],
              label_textbox_ssw:
                musterFormDialogStore.currentFormSetting?.['textbox_ssw'],
            });
          }
        }
        break;

      case FormName.Muster_10C:
        {
          let currentFormSetting = {};

          if (isCreateForm) {
            currentFormSetting = {
              label_gender_0: gender,
              date_quartal_0: quartal,
            };
          }

          if (musterFormDialogStore.isViewForm) {
            currentFormSetting = {
              label_date_quartal_0:
                musterFormDialogStore.currentFormSetting?.['date_quartal_0'],
              label_date_label_full_date_0:
                musterFormDialogStore.currentFormSetting?.[
                  'date_label_full_date_0'
                ],
              label_date_abnahmezeit_0:
                musterFormDialogStore.currentFormSetting?.[
                  'date_abnahmezeit_0'
                ],
              label_textbox_betri_0:
                musterFormDialogStore.currentFormSetting?.['textbox_betri_0'],
              label_textbox_arzt_0:
                musterFormDialogStore.currentFormSetting?.['textbox_arzt_0'],
            };
          }

          musterFormDialogActions.setCurrentMusterFormSetting({
            ...currentFormSetting,
            label_insuranceInfo_1:
              `${musterFormDialogStore?.patient?.lastName} ${musterFormDialogStore?.patient?.firstName}`.trim(),
          });
        }
        break;

      case FormName.Muster_39A:
        {
          if (musterFormDialogStore.isViewForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_month_year_date_anamnese:
                musterFormDialogStore.currentFormSetting?.['date_anamnese'],
              label_date_custom_jetzt:
                musterFormDialogStore.currentFormSetting?.['date_custom_jetzt'],
              label_date_custom_wann:
                musterFormDialogStore.currentFormSetting?.['date_custom_wann'],
            });
          } else if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              checkbox_20_29_jahre: 20 <= patientAge && patientAge <= 29,
              checkbox_30_34_jahre: 30 <= patientAge && patientAge <= 34,
              checkbox_ab_35_jahre: patientAge >= 35,
            });
          }
        }
        break;

      case FormName.Muster_13: {
        if (isCreateForm) {
          const actions =
            musterFormDialogStore.componentActions as IHeimiSelectionActions;

          if (patientAge != -1 && patientAge < 18) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              checkbox_zuzahlungsfrei_0: true,
            });
            actions.setZuzahLungsFrei(true);
          } else {
            const insuranceActive = getActiveInsurance(
              patient?.patientInfo?.insuranceInfos
            );
            if (insuranceActive?.haveCoPaymentExemptionTill) {
              const fieldNameActive = checkCopaymentExemptionTillDate(
                insuranceActive.copaymentExemptionTillDate
              )
                ? 'checkbox_zuzahlungsfrei_0'
                : 'checkbox_zuzahlungpflicht_0';
              musterFormDialogActions.setCurrentMusterFormSetting({
                [fieldNameActive]: true,
              });

              switch (fieldNameActive) {
                case 'checkbox_zuzahlungsfrei_0':
                  actions.setZuzahLungsFrei(true);
                  break;
                case 'checkbox_zuzahlungpflicht_0':
                  actions.setZuzahLungsPflicht(true);
                  break;
                default:
                  break;
              }
            } else {
              musterFormDialogActions.setCurrentMusterFormSetting({
                checkbox_zuzahlungpflicht_0: true,
              });
              actions.setZuzahLungsPflicht(true);
            }
          }
        }
        break;
      }
      case FormName.BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_bevollmachtigter: datetimeUtil.now(),
            date_label_custom_unterschrift: datetimeUtil.now(),
          });
        }
        break;
      }
      case FormName.Muster_52_2_V3: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
            label_fax_text: '„Faxnummer Krankenkasse“',
            label_fax_number: selectedInsurance?.fax,
          });
        }
        break;
      }
      case FormName.Ambulantes_Operieren_V1: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
            label_fax_number: selectedInsurance?.fax,
          });
        }
        break;
      }
      case FormName.AOK_BW_Beratungsbogen_Einbindung_SD_V7:
      case FormName.BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_erklarung_patient_0: datetimeUtil.now(),
            date_label_custom_erklarung_praxis_0: datetimeUtil.now(),
            date_abnahmezeit_0: datetimeUtil.now(),
            date_abnahmezeit1_0: datetimeUtil.now(),
          });
        }
        break;
      }
      case FormName.BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6:
      case FormName.BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_abnahmezeit: datetimeUtil.now(),
            date_abnahmezeit1: datetimeUtil.now(),
            date_label_custom_datum: datetimeUtil.now(),
          });
        }
        break;
      }
      case FormName.Ueberleitungsbogen_EK_BKK_NO_WL_V1:
      case FormName.Ueberleitungsbogen_AOK_KBS_NO_WL_V2:
      case FormName.AOK_HH_HZV_Ueberleitungsbogen_V2:
      case FormName.AOK_SH_HZV_Ueberleitungsmanagement_V3:
      case FormName.RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3: {
        // TODO: currently hard fixed based on AOK_HH_HZV_VSW_Selektivvertragsdefinition_Q4-22-1.xml file
        const faxNumber =
          formName === FormName.AOK_HH_HZV_Ueberleitungsbogen_V2
            ? '0211 8791 24716'
            : '';

        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_prescribe_0: datetimeUtil.now(),
            date_label_custom_of_today_2: datetimeUtil.now(),
            textbox_fax_number_1: faxNumber,
            textbox_fax_number_2: faxNumber,
          });
        }
        break;
      }
      case FormName.AWH_01_Kurzantrag_HZV_KinderReha_V1:
        {
          musterFormDialogActions.setCurrentMusterFormSetting({
            textbox_name_arztin_0: currentDoctor?.firstName,
            textbox_vorname_arztin_0: currentDoctor?.lastName,
            textbox_strabe_arztin_0: currentDoctor?.address,
            textbox_telefon_arztin_0: currentDoctor?.phone,
          });
        }
        break;
      case FormName.AWH_01_Checkliste_Psychosomatik_V1:
      case FormName.AWH_01_Checkliste_Somatik_V1:
      case FormName.HZV_Beleg_Muster_V3:
      case FormName.Muster_65A:
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
          });
        }
        break;

      case FormName.Begleitschreiben_FaV_V4:
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_full_date: datetimeUtil.now(),
          });
        }
        break;

      case FormName.Muster_70:
      case FormName.Muster_70A: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datumGesamtsumme:
              musterFormDialogStore.currentFormSetting?.[
                'date_label_custom_datumGesamtsumme'
              ] || +datetimeUtil.date(),
            textbox_ortGesamtsumme:
              musterFormDialogStore.currentFormSetting?.[
                'textbox_ortGesamtsumme'
              ] || currentDoctor?.bsnrCity,
          });
        }

        break;
      }
      case FormName.BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum:
              musterFormDialogStore.currentFormSetting?.[
                'date_label_custom_datum'
              ] || +datetimeUtil.date(),
            textbox_fax: selectedInsurance?.fax,
            textbox_eGKnr: musterFormDialogStore?.contractDoctor?.contractId,
            textbox_telefon: selectedInsurance?.tel,
            textbox_versicherten_number: selectedInsurance?.insuranceNumber,
          });
        }
        break;
      }

      case FormName.Muster_56: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            [MUSTER_56_FIELD_NAMES.date_56_datum_1]: datetimeUtil.now(),
            // send to show date time format for form print
            label_date_label_custom_datum_1_no_space: '',
          });
        }
        break;
      }
      case FormName.Muster_61: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            // send to show date time format for form print
            label_date_label_custom_datum_4_no_space: '',
            label_date_label_custom_datum_0_no_space: '',
            label_date_label_custom_datum2_4_no_space: '',
          });
        }
        break;
      }
      case FormName.Muster_12A: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            // send to show date time format for form print
            label_date_label_custom_vom_0_no_space: '',
            label_date_label_custom_bis_0_no_space: '',
          });
        }
        break;
      }
      case FormName.BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
            textbox_bkk: musterFormDialogStore?.contractDoctor?.contractId,
            textbox_fax: selectedInsurance?.fax,
            textbox_telefon: selectedInsurance?.tel,
            textbox_versicherten_number: selectedInsurance?.insuranceNumber,
          });
        }
        break;
      }

      case FormName.Muster_PTV_11A:
        {
          if (musterFormDialogStore.isViewForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              label_date_abnahmezeit:
                musterFormDialogStore.currentFormSetting?.['date_abnahmezeit'],
            });
          } else if (isCreateForm) {
            const prescribedDate = +datetimeUtil.date();

            musterFormDialogActions.setCurrentMusterFormSetting({
              date_label_custom_datumErklarung: prescribedDate,
              date_label_custom_ausstellungsdatum: prescribedDate,
            });
          }
        }
        break;
      case FormName.Muster_PTV_2A:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              date_label_custom_datum: datetimeUtil.now(),
              label_lastname_char: (
                musterFormDialogStore.currentFormSetting?.[
                  'label_patientInfo_line1'
                ] as string
              )?.charAt(0),
              date_custom_birthday:
                musterFormDialogStore.currentFormSetting?.[
                  'label_date_of_birth'
                ],
              area_textbox_address: addressContent,
            });
          }
        }
        break;
      case FormName.Muster_22A:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              date_label_custom_datum: datetimeUtil.now(),
              label_lastname_char: (
                musterFormDialogStore.currentFormSetting?.[
                  'label_patientInfo_line1'
                ] as string
              )?.charAt(0),
              date_custom_birthday:
                musterFormDialogStore.currentFormSetting?.[
                  'label_date_of_birth'
                ],
            });
          }
        }
        break;
      case FormName.Muster_7:
      case FormName.Muster_11:
      case FormName.Muster_20A:
      case FormName.Muster_26A:
      case FormName.Muster_27A:
      case FormName.Muster_50:
      case FormName.Muster_51:
      case FormName.Muster_52_0_V2:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              date_label_custom_datum: datetimeUtil.now(),
            });
          }
        }
        break;
      case FormName.Muster_28A:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              date_label_custom_datum1: datetimeUtil.now(),
            });
          }
        }
        break;
      case FormName.Muster_PTV_1A: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
            area_textbox_address: addressContent,
            label_patient_fullname_1:
              musterFormDialogStore.currentFormSetting?.[
                'label_patient_fullname'
              ],
            label_insurance_number_1:
              musterFormDialogStore.currentFormSetting?.[
                'label_insurance_number'
              ],
            label_ik_number_1:
              musterFormDialogStore.currentFormSetting?.['label_ik_number'],
            label_prf_nr_1:
              musterFormDialogStore.currentFormSetting?.['label_prf_nr'],
          });
        }
        break;
      }
      case FormName.Muster_PTV_12A:
        {
          if (isCreateForm) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              date_label_custom_datum: datetimeUtil.now(),
              area_textbox_address: addressContent,
            });
          }
        }
        break;
      case FormName.Muster_64: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum_1: datetimeUtil.now(),
          });
        }
        break;
      }
      case FormName.BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2:
      case FormName.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5: {
        if (isCreateForm) {
          if (
            !musterFormDialogStore?.currentFormSetting?.date_label_custom_abmel
          ) {
            musterFormDialogActions.setCurrentMusterFormSetting({
              date_label_custom_abmel: +datetimeUtil.endOfSelectedDay(),
            });
          }

          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
          });
        }
        break;
      }
      case FormName.AOK_FA_NPPP_BW_GDK_Antragsformular_V6:
      case FormName.BKK_BOSCH_FA_BW_GDK_Antragsformular_V4:
      case FormName.BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3:
      case FormName.BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2:
      case FormName.AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3:
      case FormName.BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2:
      case FormName.BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2: {
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
          });
        }
        break;
      }

      case FormName.AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12:
      case FormName.AOK_PLUS_Versichertenteilnahmeerklaerung_V6:
      case FormName.AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4:
      case FormName.AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8:
      case FormName.AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3:
      case FormName.IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12:
      case FormName.RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5:
      case FormName.SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7: {
        if (isCreateForm) {
          const data = [
            ...Array.from({ length: 6 }).map(
              (_, index) => `date_label_custom_datum_${index}`
            ),
          ].reduce((disabledDatum, fieldName) => {
            return {
              ...disabledDatum,
              [fieldName]: datetimeUtil.now(),
            };
          }, {});

          musterFormDialogActions.setCurrentMusterFormSetting(data);
        }
        break;
      }

      case FormName.AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7: {
        if (isCreateForm) {
          const data = [
            ...Array.from({ length: 6 }).map(
              (_, index) => `date_label_custom_datum_${index}`
            ),
            ...Array.from({ length: 6 }).map(
              (_, index) => `date_label_custom_datum1_${index}`
            ),
            ...Array.from({ length: 6 }).map(
              (_, index) => `date_label_custom_datum2_${index}`
            ),
          ].reduce((disabledDatum, fieldName) => {
            return {
              ...disabledDatum,
              [fieldName]: datetimeUtil.now(),
            };
          }, {});

          musterFormDialogActions.setCurrentMusterFormSetting(data);
        }
        break;
      }

      case FormName.AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3:
      case FormName.AOK_FA_BW_TE_HepCModul_V3:
      case FormName.AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2:
      case FormName.AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2:
      case FormName.AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9:
      case FormName.AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11:
      case FormName.AOK_NO_HH_Versichertenteilnahmeerklaerung_V5:
      case FormName.AWH_01_BVKJ_Anlage_7b_Osteopathie_V2:
      case FormName.AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12:
      case 'BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4':
      case FormName.BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11:
      case FormName.BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2:
      case FormName.BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3:
      case FormName.BKK_BOSCH_FA_TE_HepCModul_V4:
      case FormName.BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9:
      case FormName.BKK_VAG_FA_BW_TE_HepCModul_V2:
      case FormName.BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5:
      case FormName.BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4:
      case FormName.DAK_HZV_VersichertenTeilnahmeerklaerung_V4:
      case FormName.EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4:
      case FormName.EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7:
      case FormName.EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3:
      case FormName.EK_NO_HZV_Versichertenteilnahmeerklaerung_V2:
      case FormName.EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3:
      case FormName.EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5:
      case FormName.EK_SN_HZV_Versichertenteilnahmeerklaerung_V3:
      case FormName.EK_WL_HZV_Versichertenteilnahmeerklaerung_V2:
      case FormName.HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4:
      case FormName.LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15:
      case 'LKK_BY_Teilnahme-und_Einwilligungserklaerung_V4':
      case 'LKK_Teilnahme-und_Einwilligungserklaerung_V6':
      case FormName.TK_HZV_Versichertenteilnahmeerklaerung_V9:
      case FormName.Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6:
      case FormName.Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8:
      case FormName.Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5:
      case FormName.Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5:
      case FormName.Versichertenteilnahmeerklaerung_Online_Variante_A_V11:
      case FormName.Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V8: {
        if (isCreateForm) {
          const data = [
            ...Array.from({ length: 6 }).map(
              (_, index) => `date_label_full_date_without_dot_${index}`
            ),
            ...Array.from({ length: 6 }).map(
              (_, index) => `date_label_full_date_without_dot1_${index}`
            ),
            ...Array.from({ length: 6 }).map(
              (_, index) => `date_label_full_date_without_dot2_${index}`
            ),
          ].reduce((disabledDatum, fieldName) => {
            return {
              ...disabledDatum,
              [fieldName]: datetimeUtil.now(),
            };
          }, {});

          musterFormDialogActions.setCurrentMusterFormSetting(data);
        }
        break;
      }

      case FormName.BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1: {
        musterFormDialogActions.setCurrentMusterFormSetting({
          textbox_telefon: selectedInsurance?.tel || '',
        });
      }
      case FormName.F1050:
      case FormName.F1000:
      case FormName.F2100:
        if (isCreateForm && currentSchein) {
          let insuranceActive: InsuranceInfo | undefined = undefined;
          const insuranceInfo = patient?.patientInfo?.insuranceInfos || [];

          if (insuranceInfo.length === 1 && insuranceInfo[0].insuranceType === TypeOfInsurance.BG) {
            insuranceActive = insuranceInfo[0];
          } else if (getActiveInsurance(insuranceInfo)) {
            insuranceActive = getActiveInsurance(insuranceInfo);
          } else {
            insuranceActive = insuranceInfo.find(
              (insurance) => insurance.insuranceType === TypeOfInsurance.Public
            );
          }

          let activeInsuranceName = insuranceActive?.insuranceCompanyName;
          const isPrivatePatient =
            patient?.patientInfo?.genericInfo?.patientType ===
            PatientType.PatientType_Private;

          if (!activeInsuranceName && isPrivatePatient) {
            activeInsuranceName = (patient.patientInfo.insuranceInfos || [])
              .filter(
                (insurance) =>
                  insurance.insuranceType === TypeOfInsurance.Private
              )
              .at(-1)?.insuranceCompanyName;
          }

          const addressValue = [
            `${patient?.patientInfo.addressInfo.address.street || ''} ${
              patient?.patientInfo.addressInfo.address.number || ''
            }`.trim(),
            `${patient?.patientInfo.addressInfo.address.postCode || ''} ${
              patient?.patientInfo.addressInfo.address.city || ''
            }`.trim(),
          ].filter((value) => value);
          const companyAddressValue = [
            `${currentSchein.companyAddress.street || ''} ${
              currentSchein.companyAddress.number || ''
            }`.trim(),
            `${currentSchein.companyAddress.postCode || ''} ${
              currentSchein.companyAddress.city || ''
            }`.trim(),
          ].filter((value) => value);

          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_arrive_date_0: currentSchein.arrivalDate,
            label_time_arrive_time_0: currentSchein.arrivalDate,
            textbox_unfallversicher_0: selectedInsurance?.insuranceCompanyName,
            label_lastname_0: patient?.lastName,
            label_firstname_0: patient?.firstName,
            label_address_0: addressValue.join(', '),
            textbox_company_address_0: companyAddressValue.join(', '),
            textbox_job_0: currentSchein.jobOccupation,
            date_label_custom_accident_day_0: currentSchein.accidentDate,
            label_custom_accident_day_1: datetimeUtil.getDateString(
              currentSchein.accidentDate,
              DATE_FORMAT
            ),
            label_time_accident_time_0: currentSchein.accidentDate,
            label_active_insurance_name_0: activeInsuranceName,
            label_time_start_time_0: currentSchein.workingTimeStart,
            label_time_end_time_0: currentSchein.workingTimeEnd,
            date_label_custom_datum_0: datetimeUtil.now(),
            date_label_custom_datum_1: datetimeUtil.now(),
            label_city_bsnr_0: `${currentDoctor?.bsnrStreet || ''} ${
              currentDoctor?.bsnrCity || ''
            }`.trim(),
            label_ik_number_cost_unit_1: selectedInsurance?.ikNumber,
            textbox_rechnungsnummer_1: currentSchein.invoiceNumber,
            label_bsnr_date_0: `${
              currentDoctor?.bsnrCity || ''
            } ${datetimeUtil.getDateString(
              datetimeUtil.now(),
              DATE_FORMAT
            )}`.trim(),
          });
        }
        break;
      case FormName.F9990: {
        if (isCreateForm && currentSchein) {
          const uvGoaEntries = getTimelineServiceEntries(
            timelineState || storeTimeline.timelineState,
            currentSchein.scheinId
          ).slice(-MAXIMUM_UV_GOA_SERVICE_F9990);
          const isGeneral = currentSchein.isGeneral;

          const mappingData = uvGoaEntries.reduce(
            (mappingData, datum, index) => {
              const price = datum.price || 0;
              const sum1 = isGeneral
                ? +(mappingData['label_sum_eur1'] || 0) + price
                : +(mappingData['label_sum_eur1'] || 0);
              const sum2 = !isGeneral
                ? +(mappingData['label_sum_eur2'] || 0) + price
                : +(mappingData['label_sum_eur2'] || 0);

              return {
                ...mappingData,
                [`label_custom_rechnung${index}`]: datetimeUtil.getDateString(
                  datum.selectedDate,
                  DATE_FORMAT
                ),
                [`label_uv_goa${index}`]: datum.freeText,
                [`label_gebuhr${index}`]: isGeneral
                  ? transformPrice(price, null as any)
                  : '',
                [`label_besondere${index}`]: !isGeneral
                  ? transformPrice(price, null as any)
                  : '',
                label_sum_eur1: sum1,
                label_sum_eur2: sum2,
                label_sum_eur3: sum2,
                label_sum_eur4: sum1 + sum2,
              };
            },
            {}
          );

          musterFormDialogActions.setCurrentMusterFormSetting({
            ...mappingData,
            label_sum_eur1: transformPrice(
              mappingData['label_sum_eur1'] || 0
            ).slice(0, -2),
            label_sum_eur2: transformPrice(
              mappingData['label_sum_eur2'] || 0
            ).slice(0, -2),
            label_sum_eur3: transformPrice(
              mappingData['label_sum_eur3'] || 0
            ).slice(0, -2),
            label_sum_eur4: transformPrice(
              mappingData['label_sum_eur4'] || 0
            ).slice(0, -2),
            date_label_custom_accident_day: currentSchein.accidentDate,
            date_label_custom_datum: datetimeUtil.now(),
            textbox_rechnungsnummer: currentSchein.invoiceNumber,
            label_ik_number_cost_unit: selectedInsurance?.ikNumber,
            checkbox_allgemeine: isGeneral,
            checkbox_besondere: !isGeneral,
          });
        }
        break;
      }
      case FormName.BKK_BY_HZV_Einwilligungserklaerung_Telemedizinisches_Facharztkonsil:
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_full_date_without_dot: datetimeUtil.now(),
            date_label_full_date_without_dot1: datetimeUtil.now(),
          });
        }
        break;
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5:
      case FormName.AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4:
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum: datetimeUtil.now(),
            date_label_custom_behand: datetimeUtil.now(),
          });
        }
        break;
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3:
      case FormName.AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3:
        if (isCreateForm) {
          musterFormDialogActions.setCurrentMusterFormSetting({
            date_label_custom_datum_0: datetimeUtil.now(),
          });
        }
        break;

      default:
    }

    callback?.();
  },

  getMusterFormStore: () => {
    return musterFormStore;
  },

  closeHeimiDialog: () => {
    musterFormDialogActions.clear();
    patientFileActions.setActiveTabId(ID_TABS.HEIMI);
    heimiSelectionActions.setIsClearRemedySelected(false);
    heimiSelectionActions.setIsClearComplementaryRemedySelected(false);
  },

  getForm6PreFillData: () => {
    if (!musterFormDialogStore.isViewForm && !musterFormDialogStore.isRefill) {
      if (
        patientFileStore?.schein?.activatedSchein?.scheinMainGroup ===
        MainGroup.HZV
      ) {
        return M6_HVZ_PRE_DEFINED_TEXT;
      } else if (
        patientFileStore?.schein?.activatedSchein?.scheinMainGroup ===
        MainGroup.FAV
      )
        return M6_FAV_PRE_DEFINED_TEXT;
    }
  },
};

export const useMusterFormStore = () => {
  useEffect(() => {
    return () => {
      musterFormStore = proxy<IMusterFormProps>({
        isLoading: true,
      });
    };
  }, []);
  return useSnapshot(musterFormStore) as IMusterFormProps;
};
