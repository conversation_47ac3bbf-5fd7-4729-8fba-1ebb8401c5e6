import debounce from 'lodash/debounce';
import React, {
  memo,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import {
  BodyTextM,
  Flex,
  InfoConfirmDialog,
  Svg,
} from '@tutum/design-system/components';
import {
  Button,
  Callout,
  Checkbox,
  Intent,
  Switch,
  Tab,
  Tabs,
  Tooltip,
} from '@tutum/design-system/components/Core';
import {
  alertInProgress,
  alertSuccessfully,
  clearToaster,
} from '@tutum/design-system/components/Toaster';
import { scaleSpace } from '@tutum/design-system/styles';
import { COLOR } from '@tutum/design-system/themes/styles';
import { addEntry } from '@tutum/hermes/bff/app_mvz_bmp';
import {
  removeFromShoppingBag,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import { getPredefinedDataOfMedication } from '@tutum/hermes/bff/legacy/app_mvz_bmp';
import {
  AddToShoppingBagRequest,
  FormType,
  MedicineType,
  addToShoppingBag,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine';
import {
  Order,
  SearchType,
  Sort,
  SortField,
  getMMIDBStatus,
  useMutationGetBlueHandLetters,
  useMutationGetRedHandLetters,
} from '@tutum/hermes/bff/legacy/app_mvz_medicine_kbv';
import { HandLetter, HintsAndWarning } from '@tutum/hermes/bff/legacy/medicine_common';
import { MedicationInformation } from '@tutum/hermes/bff/repo_bmp_common';
import {
  AddEntryRequest,
  GroupType,
} from '@tutum/hermes/bff/service_domains_bmp';
import I18n, { II18nFixedNamespace } from '@tutum/infrastructure/i18n';
import { Paths } from '@tutum/infrastructure/i18n/i18n.context';
import { YEAR_MONTH_DAY_FORMAT } from '@tutum/infrastructure/shared/date-format';
import datetimeUtil from '@tutum/infrastructure/utils/datetime.util';
import { checkIsPrivateSchein } from '@tutum/mvz/_utils/scheinFormat';
import { IMenuItem } from '@tutum/mvz/components/categories-search';
import { KEYCODE } from '@tutum/mvz/constant/keycode';
import { useSettingStore } from '@tutum/mvz/hooks/useSetting.store';
import type CommonLocales from '@tutum/mvz/locales/en/Common.json';
import type MedicationI18n from '@tutum/mvz/locales/en/Medication.json';
import { MedicationElementId } from '@tutum/mvz/module_action-chain/actions/medication';
import { useMedication } from '@tutum/mvz/module_medication/context/MedicationProvider';
import MedicationDialog from '@tutum/mvz/module_medication/medication-plan/medication-dialog/MedicationDialog.styled';
import MedicationPlan from '@tutum/mvz/module_medication/medication-plan/MedicationPlan.styled';
import PrescribedMedication from '@tutum/mvz/module_medication/prescribed-medication/PrescribedMedication.styled';
import RemoveShoppingBagDialog from '@tutum/mvz/module_medication/remove-shopping-bag-dialog/RemoveShoppingBagDialog.styled';
import MedicineShoppingBagHook from '@tutum/mvz/module_medication_kbv/hooks/MedicineShoppingBag.hook';
import MedicationPrintPreview from '@tutum/mvz/module_medication_kbv/medication-print-preview/MedicationPrintPreview.styled';
import FilterPackageSize from '@tutum/mvz/module_medication_kbv/medication/filter/FilterPackageSize';
import SecondaryInfoDialog from '@tutum/mvz/module_medication_kbv/medication/secondary-info/SecondaryInfoDialog.styled';
import MedicationShoppingBag from '@tutum/mvz/module_medication_kbv/shopping-bag/MedicationShoppingBag.styled';
import PatientManagementContext from '@tutum/mvz/module_patient-management/contexts/patient-management/PatientManagementContext';
import { catalogOverviewActions } from '@tutum/mvz/module_patient-management/patient-file/CatalogsOverview.store';
import {
  IScheinPatientFileStore,
  patientFileStore,
  usePatientFileStore,
} from '@tutum/mvz/module_patient-management/patient-file/PatientFile.store';
import { ID_TABS } from '@tutum/mvz/module_patient-management/patient-file/PatientFile.type';
import { IMvzTheme } from '@tutum/mvz/theme';
import GlobalContext from '../../contexts/Global.context';
import {
  medicationShoppingBagActions,
  useMedicationShoppingBagStore,
} from '../shopping-bag/MedicationShoppingBag.store';
import {
  USE_MUSTER_16_FOR_OTC_DRUG_FOR_LESS_18_YEARS_OLD,
  medicationUtil,
  now,
} from '../utils/medication-util';
import Alternatives from './alternatives';
import ChangePackageSizeView from './change-package-size-view';
import GeneralGBA from './general-gba';
import { getInformationByBSNR } from './helpers';
import IWWRegulationsDialog from './iww-regulations/IWWRegulationsDialog.styled';
import LettersDialog from './LettersDialog/LettersDialog.styled';
import MedicationTable from './medication-table/MedicationTable.styled';
import { medicationActions, useMedicationStore } from './MedicationKBV.store';
import NoPackageSizeDialog from './no-package-size-dialog';
import NoSubtanceDialog from './no-subtance-dialog';
import PrescribedMedicationStatistics from './prescribed-medication';
import PriceComparision from './price-comparision';
import RefillMedicationView from './refill-medication-view/RefillMedicationView.styled';
import SearchMedicationBox, { IParsedQuery } from './search-medication-box';
import SecondLayerDialog from './second-layer/SecondLayerDialog.styled';
import { useListenViewMedicationForm } from '@tutum/hermes/bff/app_mvz_medicine';
import { useCurrentSchein } from '@tutum/mvz/module_patient-management/patient-file/hooks/useCurrentSchein.hook';
import useCheckContractSupport from '@tutum/mvz/module_patient-management/hooks/useCheckContractSupport';

const IconList = '/images/icons_medicationfbv/list.svg';
const warnInfoCircleIcon = '/images/info-solid-warn.svg';
const RedHandIcon = '/images/red-hand.svg';

const InfoLightInRGB = `rgb(${COLOR.INFO_SECONDARY_PRESSED.replace('#', '')
  .match(/.{2}/gi)
  ?.map((sg) => parseInt(sg, 16))
  .join(', ')})`;
const pseudoHoverClassName = 'pseudo-hover';

export interface IMedicationProps {
  theme?: IMvzTheme;
  className?: string;
  isOpen: boolean;
}

export enum MEDICATION_TABS {
  PRESCRIBED_MEDICATION = 'PRESCRIBED_MEDICATION',
  MEDICATION_PLAN = 'MEDICATION_PLAN',
}

export enum SecondaryViewType {
  PriceComparison = 'PriceComparison',
  Alternatives = 'Alternatives',
}

interface IGroupCheckboxesSearch {
  isTradeName: boolean;
  isSubstanceName: boolean;
  isManufaturer: boolean;
}

interface IGetMedicationResultsParams {
  medicineQuery: IParsedQuery;
  isDiscounted: boolean;
  page: number;
  rowsPerPage: number;
  sortType: Sort;
  isChangeConcentration: boolean;
  isShowRegistered: boolean;
  groupCheckboxesSearch: IGroupCheckboxesSearch;
  memoizedIkNumber: number;
  isSVPatient: boolean;
  schein: IScheinPatientFileStore;
  patientId: string;
  listSubstance: IMenuItem[];
  packageSizes: string[];
}

const IWW = 'IWW';

const RefillToastKey = 'RefillToastKey';

const MedicationKBV = ({
  className,
  isOpen,
  t,
}: IMedicationProps & II18nFixedNamespace<Paths<typeof MedicationI18n>>) => {
  const { t: tButtonActions } = I18n.useTranslation<
    keyof typeof CommonLocales.ButtonActions
  >({
    namespace: 'Common',
    nestedTrans: 'ButtonActions',
  });

  const globalContext = useContext(GlobalContext.instance);
  const currentLoggedinUser = globalContext.useGetLoggedInUserProfile();
  const {
    patientManagement: { patient, selectedContractDoctor },
  } = useContext(PatientManagementContext.instance);

  const [selectedId, setSelectedId] = useState<MEDICATION_TABS>(
    MEDICATION_TABS.PRESCRIBED_MEDICATION
  );
  const [isComparePrice, setIsComparePrice] = useState(false);
  const [isShowPopupUpdateStatus, setIsShowPopupUpdateStatus] = useState(false);

  const store = useMedicationStore();
  const settingStore = useSettingStore();
  const medicationContext = useMedication();

  useEffect(() => {
    getPredefinedDataOfMedication().then((res) => {
      medicationContext.setMedicationState((prevState) => ({
        ...prevState,
        ...res.data,
      }));
      medicationActions.initUnits(res.data);
    });
  }, []);

  const bsnr = currentLoggedinUser?.bsnr;
  MedicineShoppingBagHook.useMedicineShoppingBagHook({
    doctorId: store.isStatistics ? undefined : selectedContractDoctor?.doctorId,
    patientId: store.isStatistics ? undefined : patient?.id,
    bsnr: store.isStatistics ? bsnr : undefined,
    contractId: selectedContractDoctor?.contractId,
  });
  const { shoppingBag } = useMedicationShoppingBagStore();

  const currentContractDoctor = (
    selectedContractDoctor?.availableDoctor || []
  ).find((item) => selectedContractDoctor?.doctorId === item?.id);
  const lanr = currentContractDoctor?.lanr;
  const isSvPatient = patientFileStore.isSvPatient;

  // --- test ARV
  // const bsnr = '033432400';
  // const lanr = '123456729';
  // pzn: '03447061',

  const [isShow2ndLayer, setShow2ndLayer] = useState(false);
  const {
    isShowShoppingBag,
    medicineQuery,
    isDisplaySearchMedicineResult,
    page,
    rowsPerPage,
    selectAllRowsItem,
    isChangeConcentration,
    isCheckedMMI,
  } = store;
  const [isShowIwwRegulations, setShowIwwRegulations] = useState(false);
  const [isDiscounted, setIsDiscounted] = useState(
    settingStore.medication.isOnlyDiscountInSearch
  );
  const { setMedicineQuery, setPage, setRowsPerPage } = medicationActions;
  const [sortType, setSortType] = useState<Sort>({
    field: SortField.Substance,
    order: Order.Asc,
  });
  const [pzn, setPzn] = useState<string>('');
  const [isSubstitution, setIsSubstitution] = useState<boolean>(false);
  const [hintsAndWarnings, setHintsAndWarnings] = useState<HintsAndWarning[] | undefined> (undefined);
  const [secondaryLayer, setSecondaryLayer] = useState<{
    type;
    info;
  } | null>(null);
  const [isRemoveSBDialog, setIsRemoveSBDialog] = useState<boolean>(false);
  const [isLoadingRemoveSBDialog, setIsLoadingRemoveSBDialog] =
    useState<boolean>(false);
  // const [isOpenRefillDialog, setIsOpenRefillDialog] = useState(false);
  const [searchType, setSearchType] = useState<IMenuItem | undefined>(
    undefined
  );
  const [openBmpMedicationDialog, setOpenBmpMedicationDialog] =
    useState<boolean>(false);
  const [isMP, setIsMP] = useState(false);
  const [isOpenGeneralGBA, setIsOpenGeneralGBA] = useState<boolean>(false);
  const [isShowRegistered, setIsShowRegistered] = useState<boolean>(false);
  const [packageSizes, setPackageSizes] = useState<string[]>([]);
  const [groupCheckboxesSearch, setGroupCheckboxesSearch] =
    useState<IGroupCheckboxesSearch>({
      isTradeName: true,
      isSubstanceName: true,
      isManufaturer: true,
    });
  const [listSubstance, setListSubstance] = useState<IMenuItem[]>([]);
  const [isShowLettersDialog, setShowLettersDialog] = useState<boolean>(false);
  const [handLettersType, setHandLettersType] = useState<'red' | 'blue'>('red');
  const [hasFilterHandLetters, setFilterHandLetters] = useState<boolean>(false);
  const [redHandLetters, setRedHandLetters] = useState<
    HandLetter[] | undefined
  >(undefined);
  const [blueHandLetters, setBlueHandLetters] = useState<
    HandLetter[] | undefined
  >(undefined);
  const currentSchein = useCurrentSchein();
  const { isContractSupport: hasSupportVSST550 } = useCheckContractSupport(
    ['VSST550'],
    [currentSchein?.hzvContractId]
  );

  const {
    isSvPatient: isSVPatient,
    schein,
    barcodeMedicationPlanContent,
    activeTabId,
  } = usePatientFileStore();

  const medKBVRef = useRef<HTMLDivElement | null>(null);

  useListenViewMedicationForm((payload) => {
    const { patientId } = payload;
    if (!patientId || !patient || patientId !== patient.id) {
      return;
    }
    medicationContext.setViewMedicationForm(payload);
  });

  const memoizedIkNumber = useMemo(() => {
    const insuranceInfo = patient?.patientInfo.insuranceInfos.find(
      (insur) => insur.id === schein.activatedSchein?.insuranceId
    );

    return store.isStatistics
      ? (getInformationByBSNR(bsnr).ikNumber ?? 0)
      : (insuranceInfo?.ikNumber ?? 0);
  }, [patient, store.isStatistics, bsnr, schein.activatedSchein]);

  useEffect(() => {
    if (activeTabId === ID_TABS.MEDICATION && !isCheckedMMI) {
      medicationActions.setIsCheckedMMI(true);

      getMMIDBStatus({ date: now.getTime() }).then(async (res) => {
        const isValid = await medicationUtil.validToShowPopupMMI(res.data);
        if (isValid) {
          setIsShowPopupUpdateStatus(true);
        }
      });
    }
  }, [activeTabId, isCheckedMMI]);

  useEffect(() => {
    if (patient && selectedContractDoctor) {
      medicationShoppingBagActions.setPatientDoctor(
        patient,
        selectedContractDoctor
      );
    }
  }, [patient, selectedContractDoctor]);

  useEffect(() => {
    if (
      !selectedContractDoctor?.contractId ||
      !selectedContractDoctor?.chargeSystemId
    ) {
      return;
    }

    catalogOverviewActions
      .doesContractSupportFunctions(
        [USE_MUSTER_16_FOR_OTC_DRUG_FOR_LESS_18_YEARS_OLD],
        selectedContractDoctor.contractId,
        selectedContractDoctor.chargeSystemId
      )
      .then((isSupport) => {
        medicationContext.setUseMuster16ForOtcDrugLess18YearsOld(isSupport);
      });
  }, [
    selectedContractDoctor?.doctorId,
    selectedContractDoctor?.chargeSystemId,
  ]);

  useEffect(() => {
    if (isOpen && bsnr) {
      medicationActions.checkIwwRegulations({
        bsnr,
      });
    }
  }, [isOpen, bsnr]);

  useEffect(() => {
    if (isDiscounted !== settingStore.medication.isOnlyDiscountInSearch) {
      setIsDiscounted(settingStore.medication.isOnlyDiscountInSearch);
    }
  }, [settingStore.medication.isOnlyDiscountInSearch]);

  const handleClickMedicationLink = useCallback((atcCode: string) => {
    setShowIwwRegulations(false);
    const sort = {
      field: SortField.Substance,
      order: Order.Asc,
    };
    setMedicineQuery({
      type: SearchType.ATCCode,
      keyword: atcCode,
    });
    setSearchType({
      label: t('SearchMedicationBox.atcCode'),
      value: SearchType.ATCCode,
    });
    setPage(1);
    setSortType(sort);
    medicationActions.setIsDisplaySearchMedicineResult(true);
  }, []);

  const getMedicationResults = useCallback(
    debounce(
      ({
        medicineQuery,
        isDiscounted,
        page,
        rowsPerPage,
        sortType,
        isChangeConcentration,
        isShowRegistered,
        groupCheckboxesSearch,
        memoizedIkNumber,
        isSVPatient,
        schein,
        patientId,
        listSubstance,
        packageSizes,
      }: IGetMedicationResultsParams) => {
        if (!isChangeConcentration) {
          const isSearchTypeAll = medicineQuery?.type === SearchType.All;
          const searchModes = {
            tradeName: !isSearchTypeAll
              ? false
              : groupCheckboxesSearch?.isTradeName,
            activeSubstanceName: !isSearchTypeAll
              ? false
              : groupCheckboxesSearch?.isSubstanceName,
            manufataturer: !isSearchTypeAll
              ? false
              : groupCheckboxesSearch?.isManufaturer,
          };
          medicationActions.getMedicationResults({
            type: medicineQuery?.type,
            value: medicineQuery?.keyword ?? '',
            isDiscount: isDiscounted,
            ikNumber: memoizedIkNumber,
            page,
            pageSize: rowsPerPage,
            sort: sortType,
            isSV: isSVPatient,
            contractId: schein?.activatedSchein?.hzvContractId ?? undefined,
            referenceDate: datetimeUtil.dateTimeFormat(
              datetimeUtil.now(),
              YEAR_MONTH_DAY_FORMAT
            ),
            bsnr: currentLoggedinUser.bsnr!,
            lanr: currentLoggedinUser.lanr,
            isShowRegistered,
            searchModes,
            patientId: patientId,
            isMonoSearch: medicineQuery?.type === SearchType?.Substances,
            moleculeIds:
              listSubstance?.map((item) => Number(item.value)) ?? null,
            isPrivateSchein: checkIsPrivateSchein(schein?.activatedSchein),
            packageSizes,
          });
        }
      },
      500
    ),
    [currentLoggedinUser]
  );

  const triggerSearch = () => {
    getMedicationResults({
      medicineQuery,
      isDiscounted,
      page,
      rowsPerPage,
      sortType,
      isChangeConcentration,
      isShowRegistered,
      groupCheckboxesSearch,
      memoizedIkNumber,
      isSVPatient,
      schein,
      patientId: patient?.id!,
      listSubstance,
      packageSizes,
    });
  };


  useEffect(() => {
    if (isDisplaySearchMedicineResult) {
      getMedicationResults({
        medicineQuery,
        isDiscounted,
        page,
        rowsPerPage,
        sortType,
        isChangeConcentration,
        isShowRegistered,
        groupCheckboxesSearch,
        memoizedIkNumber,
        isSVPatient,
        schein,
        patientId: patient?.id!,
        listSubstance,
        packageSizes,
      });
    }
  }, [
    page,
    rowsPerPage,
    isDiscounted,
    medicineQuery?.keyword,
    isChangeConcentration,
    isShowRegistered,
    JSON.stringify(groupCheckboxesSearch),
    memoizedIkNumber,
    isSVPatient,
    schein,
    bsnr,
    lanr,
    patient?.id,
    listSubstance,
    isDisplaySearchMedicineResult,
    sortType,
    packageSizes,
  ]);

  // if skip all refill (medicine price color not change), no need open refill dialog -> add medicine to shopping bag
  // else -> process refill medicine in refill dialog
  const handleRefillAndPrescribe = async (isOpenPrintPreview: boolean) => {
    const isSkipAllRefill =
      patientFileStore.isSvPatient ||
      (await medicationActions.preCheckRefill(
        store.refill.data,
        memoizedIkNumber,
        isSvPatient,
        selectedContractDoctor?.contractId,
        checkIsPrivateSchein(schein?.activatedSchein)
      ));


    if (isSkipAllRefill) {
      await medicationActions.prescribeShoppingBag(
        patient,
        selectedContractDoctor,
        memoizedIkNumber,
        store.refill.data,
        store.isStatistics ? bsnr : undefined,
        store.isStatistics ? currentLoggedinUser.id : undefined
      );
      medicationContext.setShowPrintPreviewState(isOpenPrintPreview);
      medicationActions.setRefillData([]);
      medicationActions.setIsProcessingRefillMedication(false);
    }
    medicationActions.setIsOpenRefillDialog(!isSkipAllRefill);
    clearToaster(RefillToastKey);
  };

  useEffect(() => {
    const handleRefill = async () => {
      if (store.isProcessingRefillMedication) {
        if (!store.isAddToRefill && shoppingBag?.medicines?.length) {
          setIsRemoveSBDialog(true);
        } else {
          alertInProgress(t('SecondLayerDialog.processingRefill'), {
            key: RefillToastKey,
          });
          await handleRefillAndPrescribe(!store.isAddToRefill);
        }
      }
    };
    handleRefill();
  }, [store.isProcessingRefillMedication, store.isAddToRefill, patientFileStore.isSvPatient]);

  const handleSetPzn = (pzn: string, isComparePrice = false) => {
    setIsComparePrice(false);
    setPzn(pzn);
    setIsSubstitution(isComparePrice);
    setShow2ndLayer(true);
    if (medKBVRef.current) {
      Array.from(
        medKBVRef.current.querySelectorAll('.' + pseudoHoverClassName) || []
      ).forEach((el) => el.classList?.remove(pseudoHoverClassName));
    }
  };

  const showSecondary = (type, info, isCloseSeconLayer = true) => {
    setSecondaryLayer({ type, info });
    isCloseSeconLayer && setShow2ndLayer(false);
  };

  const clearSearchBox = () => {
    setMedicineQuery({
      type: SearchType.All,
      keyword: '',
    });
    setListSubstance([]);
    setSearchType(undefined);
    medicationActions.setIsDisplaySearchMedicineResult(false);
    medicationActions.resetMedicationResults();
    medicationActions.setIsChangeConcentration(false);
    medicationActions.setSelectAllRowsItem(false);
    medicationActions.setRowsPerPage(30);
  };

  const handleCloseDialog = (isClearSearchBox: boolean) => {
    setShow2ndLayer(false);
    setPzn('');
    setIsMP(false);
    medicationActions.clearIWWListeInfo();
    isClearSearchBox && clearSearchBox();
  };
  const closeDialogWhileAddedBmp = () => {
    clearSearchBox();
    handleCloseDialog(true);
    setSelectedId(MEDICATION_TABS.MEDICATION_PLAN);
  };
  const handleCloseSecondaryView = (isClose2ndLayer = true) => {
    setIsSubstitution(false);
    setSecondaryLayer(null);
    medicationActions.clearAlternativeInfo();
    medicationActions.clearAlternativeARVInfo();
    isClose2ndLayer && setShow2ndLayer(false);
    return false;
  };

  const handleAddShoppingBagAfter = () => {
    handleCloseDialog(true);
    handleCloseSecondaryView(false);
    clearSearchBox();
  };

  const handleRedHandLetters = (handLetters: HandLetter[]) => {
    setRedHandLetters(handLetters);
    setBlueHandLetters(undefined);
    setShowLettersDialog(true);
    setHandLettersType('red');
    setFilterHandLetters(false);
  };

  const handleBlueHandLetters = (handLetters: HandLetter[]) => {
    setRedHandLetters(undefined);
    setBlueHandLetters(handLetters);
    setShowLettersDialog(true);
    setHandLettersType('blue');
    setFilterHandLetters(false);
  };

  const shouldStopClickOutside = useMemo(() => {
    return (
      isShowLettersDialog ||
      isShowPopupUpdateStatus ||
      isOpenGeneralGBA ||
      openBmpMedicationDialog ||
      !!medicationContext.secondaryInfo
    );
  }, [
    isShowLettersDialog,
    isShowPopupUpdateStatus,
    isOpenGeneralGBA,
    openBmpMedicationDialog,
    medicationContext.secondaryInfo,
  ]);

  const renderSecondLayer = useMemo(() => {
    return (
      <SecondLayerDialog
        parentViewType={secondaryLayer?.type}
        handleAddShoppingBagAfter={handleAddShoppingBagAfter}
        isOpen={isShow2ndLayer}
        shouldStopClickOutside={shouldStopClickOutside}
        handleCloseDialog={() => handleCloseDialog(false)}
        pzn={pzn}
        isSvPatient={isSVPatient}
        isSubstitution={isSubstitution}
        ikNumber={`${memoizedIkNumber}`}
        hintsAndWarnings={hintsAndWarnings}
        handlePriceComparison={(inp) => {
          setIsComparePrice(true);
          showSecondary(SecondaryViewType.PriceComparison, inp);
        }}
        handleAlternatives={(inp) => {
          medicationActions.clearAlternativeInfo();
          showSecondary(SecondaryViewType.Alternatives, inp, false);
        }}
        patient={patient}
        isMP={isMP}
        selectedContractDoctor={selectedContractDoctor}
        bsnr={bsnr}
        lanr={lanr}
        doctorId={currentLoggedinUser?.id}
        setShowShoppingBag={medicationActions.setShowShoppingBag}
        onClickMedicationLink={(atcCocde) => {
          handleClickMedicationLink(atcCocde);
          setShow2ndLayer(false);
        }}
        closeDialogWhileAddedBmp={closeDialogWhileAddedBmp}
        isPrivateSchein={checkIsPrivateSchein(schein?.activatedSchein)}
        handleRedHand={handleRedHandLetters}
        handleBlueHand={handleBlueHandLetters}
      />
    );
  }, [
    pzn,
    isShow2ndLayer,
    isComparePrice,
    isMP,
    schein?.activatedSchein,
    secondaryLayer?.type,
    shouldStopClickOutside,
    isSVPatient,
    isSubstitution,
    hintsAndWarnings,
  ]);

  const medNavigation = (evt: KeyboardEvent) => {
    const hasCategoriesSearchPopover = !!document.querySelector(
      '.sl-categories-search-popover .bp5-menu'
    );
    if (hasCategoriesSearchPopover) return;

    const tableRows = Array.from(
      medKBVRef.current?.querySelectorAll('.rdt_TableRow') || []
    );
    if (tableRows.length <= 0) {
      return;
    }
    const activeIndex = tableRows.findIndex((item: HTMLElement) => {
      if (medKBVRef.current?.querySelector('.' + pseudoHoverClassName)) {
        return item.classList.contains(pseudoHoverClassName);
      }
      return (
        window.getComputedStyle(item).getPropertyValue('background-color') ===
        InfoLightInRGB
      );
    });
    tableRows.forEach((el) => {
      el.classList.remove(pseudoHoverClassName);
    });
    const nextIndex =
      activeIndex < tableRows.length - 1
        ? activeIndex + 1
        : tableRows.length - 1;
    const prevIndex = activeIndex > 0 ? activeIndex - 1 : 0;
    switch (evt.key) {
      case KEYCODE.ArrowDown:
        tableRows[nextIndex].classList.add(pseudoHoverClassName);
        tableRows[nextIndex].scrollIntoView();
        break;
      case KEYCODE.ArrowUp:
        tableRows[prevIndex].classList.add(pseudoHoverClassName);
        tableRows[prevIndex].scrollIntoView();
        break;
      case KEYCODE.Enter:
        (
          tableRows[activeIndex]?.querySelector(
            '.clickable-item'
          ) as HTMLElement
        )?.click();
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (barcodeMedicationPlanContent) {
      setSelectedId(MEDICATION_TABS.MEDICATION_PLAN);
    }

    // bind keyboard actions...
    if (medKBVRef.current) {
      medKBVRef.current.addEventListener('keydown', medNavigation);
    }
    return () => {
      if (medKBVRef.current) {
        medKBVRef.current.removeEventListener('keydown', medNavigation);
      }
      medicationActions.resetMedicationResults();
      medicationActions.setMedicineQuery({ type: SearchType.All, keyword: '' });
    };
  }, []);

  const MedicationTabs = useMemo(() => {
    if (isDisplaySearchMedicineResult) return null;
    if (store.isStatistics) {
      return (
        <PrescribedMedicationStatistics
          isSVPatient={isSVPatient}
          ikNumber={memoizedIkNumber}
          contractId={schein?.activatedSchein?.hzvContractId}
          bsnr={bsnr}
        />
      );
    }
    return (
      <Flex>
        <Tabs
          className="sl-medication-tabs"
          id="medicationTabs"
          defaultSelectedTabId={MEDICATION_TABS.PRESCRIBED_MEDICATION}
          selectedTabId={selectedId}
          onChange={(newTabId: MEDICATION_TABS) => setSelectedId(newTabId)}
        >
          <Tab
            id={MEDICATION_TABS.PRESCRIBED_MEDICATION}
            title={
              <Flex className="sl-tab-header">
                {t('Medication.prescribedMedication')}
              </Flex>
            }
            panel={
              <PrescribedMedication
                isSVPatient={isSVPatient}
                ikNumber={memoizedIkNumber}
                contractId={schein.activatedSchein?.hzvContractId}
              />
            }
          />
          <Tab
            id={MEDICATION_TABS.MEDICATION_PLAN}
            title={
              <Flex className="sl-tab-header">
                {t('Medication.medicationPlan')}
              </Flex>
            }
            panel={
              <MedicationPlan
                currentId={selectedId}
                isSVPatient={isSVPatient}
                ikNumber={memoizedIkNumber}
                contractId={schein.activatedSchein?.hzvContractId}
                pzn={pzn}
                handleSetPzn={(pzn) => {
                  handleSetPzn(pzn);
                  setIsMP(true);
                }}
              />
            }
          />
        </Tabs>
      </Flex>
    );
  }, [
    store.isStatistics,
    isDisplaySearchMedicineResult,
    selectedId,
    isSVPatient,
    schein.activatedSchein,
    pzn,
    bsnr,
  ]);

  const handleSort = (field: SortField) => {
    const sort = {
      field,
      order:
        sortType?.field !== field
          ? Order.Desc
          : sortType?.order === Order.Asc
            ? Order.Desc
            : Order.Asc,
    };
    setSortType(sort);
  };

  const handleOnCloseGeneralGBA = useCallback(() => {
    setIsOpenGeneralGBA(false);
  }, []);

  const handleSearch = (parsedQueryConvert: IParsedQuery) => {
    const order =
      groupCheckboxesSearch.isSubstanceName || !!searchType
        ? Order.Asc
        : Order.Desc;
    const sort = {
      field: SortField.Substance,
      order,
    };
    setMedicineQuery({
      ...parsedQueryConvert,
      type: parsedQueryConvert?.type ?? SearchType.All,
    });
    if (!parsedQueryConvert?.keyword?.length) {
      setMedicineQuery({
        keyword: '',
        type: SearchType.All,
      });
      medicationActions.setIsFavouriteToggle(true);
    }
    setPage(1);
    setSortType(sort);
  };

  const handleToggleIsDiscounted = useCallback(() => {
    setIsDiscounted(!isDiscounted);
    setPage(1);
  }, [isDiscounted]);

  const handleToggleIsFavourite = () => {
    medicationActions.setIsFavouriteToggle(!store.isFavourite);
    setPage(1);
    triggerSearch();
  };

  const isShowWarningDiscount =
    store.medicationResults?.isHaveDiscountProduct === false &&
    store.medicationResults?.total > 0,
    isShowWarningFavourite = !store.isFavourite && store.isDisableFavourite;

  const maxHeightTable = useMemo(() => {
    let maxHeight = 0;

    if (isShowWarningDiscount) {
      maxHeight += 51;
    }

    if (isShowWarningFavourite) {
      maxHeight += 51;
    }

    return `calc(100vh - 283px${maxHeight ? ` - ${maxHeight}px` : ''})`;
  }, [isShowWarningDiscount, isShowWarningFavourite]);

  const rendershoppingBag = isShowShoppingBag && (
    <MedicationShoppingBag
      isOpen={isShowShoppingBag}
      bsnr={store.isStatistics ? bsnr : undefined}
      handleCloseDialog={(shouldFocusSearchInput = true) => {
        medicationActions.setShowShoppingBag(false);

        if (shouldFocusSearchInput && selectedId !== MEDICATION_TABS.MEDICATION_PLAN) {
          setTimeout(() => {
            const elem = document.querySelector(
              `[data-sl-action-chain-element-id=${MedicationElementId.INPUT_SEARCH}]`
            ) as HTMLInputElement;
            if (elem) {
              elem.focus();
              medicationActions.setIsDisplaySearchMedicineResult(true);
            }
          });
        }
      }}
    />
  );

  const alternativeDisplay = useMemo(() => {
    if (!secondaryLayer) {
      return null;
    }

    return (
      <Alternatives
        pzn={pzn}
        isSVPatient={isSVPatient}
        inputRowsPerPage={rowsPerPage}
        handleSetPzn={handleSetPzn}
        arvInfo={secondaryLayer?.info}
        handleClose={() => handleCloseSecondaryView(false)}
        ikNumber={`${memoizedIkNumber}`}
        contractId={schein?.activatedSchein?.hzvContractId}
        close2ndLayer={() => setShow2ndLayer(false)}
      />
    );
  }, [
    secondaryLayer,
    pzn,
    isSVPatient,
    rowsPerPage,
    memoizedIkNumber,
    schein?.activatedSchein?.hzvContractId,
  ]);

  const handleClickIwwBtn = () => {
    if (store.iwwRegulation.data == null) {
      medicationActions.getIwwRegulations({
        bsnr: bsnr!,
      });
    }

    setShowIwwRegulations(true);
  };

  const handleCloseRefillView = () => {
    medicationActions.setIsProcessingRefillMedication(false);
    medicationActions.setIsOpenRefillDialog(false);
  };

  const onCancelRemoveSBDialog = () => {
    medicationActions.setIsProcessingRefillMedication(false);
    medicationContext.setInRefillProcess(false);
    medicationActions.resetPackageSizeStore();
    setIsRemoveSBDialog(false);
  };

  const onConfirmRemoveSBDialog = () => {
    setIsLoadingRemoveSBDialog(true);
    removeFromShoppingBag({
      shoppingBagId: shoppingBag?.shoppingBagId,
      doctorId: selectedContractDoctor?.doctorId,
      patientId: patient?.id,
      bsnr: store.isStatistics ? bsnr : undefined,
    })
      .then(async () => {
        await handleRefillAndPrescribe(true);
        setIsRemoveSBDialog(false);
      })
      .catch((error) => console.error(error))
      .finally(() => {
        setIsLoadingRemoveSBDialog(false);
      });
  };

  const bagSize = (shoppingBag?.medicines || []).length;
  const renderMedicineInShoppingBagItemCount =
    bagSize > 0 ? (
      <div className="bag-counter">
        <span>{bagSize}</span>
      </div>
    ) : null;

  const handleAddToShoppingBag = async () => {
    const request: AddToShoppingBagRequest = {
      patientId: patient?.id,
      doctorId: store.isStatistics
        ? currentLoggedinUser.id
        : selectedContractDoctor.doctorId,
      ikNumber: memoizedIkNumber,
      medicine: {
        type: MedicineType.FreeText,
        name: store.medicineQuery?.keyword!,
        quantity: 1,
        currentFormType: checkIsPrivateSchein(schein.activatedSchein)
          ? FormType.Private
          : FormType.KREZ, // K-Rez form for freetext medication
      },
      contractId: selectedContractDoctor.contractId,
      bsnr: store.isStatistics ? bsnr : undefined,
      assignedToBsnrId: store.isStatistics
        ? currentLoggedinUser.bsnrId
        : globalContext.getBsnrIdByDoctorId(selectedContractDoctor.doctorId),
    };
    await addToShoppingBag(request);
    medicationActions.setShowShoppingBag(true);
    alertSuccessfully(t('SecondLayerDialog.addToShoppingBag'));
  };

  const handleOpenMedicationDialog = () => {
    setOpenBmpMedicationDialog(true);
  };

  const handleCloseMedicationDialog = () => {
    setOpenBmpMedicationDialog(false);
  };

  const handleSaveMedication = async (
    medicationInformation: MedicationInformation
  ) => {
    const requestAddEntry: AddEntryRequest = {
      patientId: patient?.id!,
      contractId: selectedContractDoctor.contractId,
      doctorId: selectedContractDoctor.doctorId!,
      encounterCase: selectedContractDoctor.encounterCase!,
      type: GroupType.MEDICATION,
      medicationInformation: {
        ...medicationInformation,
        isKBVMedication: true,
      },
    };
    await addEntry(requestAddEntry);
    alertSuccessfully(t('SecondLayerDialog.updateSuccess'));
  };

  const handleChangeGroupCheckboxesSearch = debounce(
    (event, name: keyof IGroupCheckboxesSearch) => {
      setGroupCheckboxesSearch({
        ...groupCheckboxesSearch,
        [name]: event?.target?.checked,
      });
      if (name === 'isSubstanceName') {
        const order = event?.target?.checked ? Order.Asc : Order.Desc;
        const sort = {
          field: SortField.Substance,
          order,
        };
        setSortType(sort);
      }
    },
    200
  );

  const getRedHandLetters = useMutationGetRedHandLetters({
    onSuccess: (resp) => {
      setRedHandLetters(resp.data.redHandLetters);
      setBlueHandLetters(undefined);
    },
  });
  const getBlueHandLetters = useMutationGetBlueHandLetters({
    onSuccess: (resp) => {
      setRedHandLetters(undefined);
      setBlueHandLetters(resp.data.blueHandLetters);
    },
  });

  if (secondaryLayer) {
    if (secondaryLayer.type === SecondaryViewType.PriceComparison) {
      return (
        <>
          <PriceComparision
            pzn={pzn}
            ikNumber={`${memoizedIkNumber}`}
            isSVPatient={isSVPatient}
            isComparePrice={isComparePrice}
            contractId={schein?.activatedSchein?.hzvContractId}
            handleSetCurrentMedicine={(medicine) => {
              setHintsAndWarnings(medicine.hintsAndWarnings);
              handleSetPzn(pzn, true);
            }}
            inputRowsPerPage={rowsPerPage}
            handleClose={handleCloseSecondaryView}
            priceComparisonGroup={secondaryLayer?.info?.priceComparisonGroup}
          />
          {renderSecondLayer}
          {rendershoppingBag}
          {medicationContext.showPrintReview && (
            <MedicationPrintPreview
              clearSearchBox={clearSearchBox}
              bsnr={store.isStatistics ? bsnr : undefined}
              medicineType="kbv"
            />
          )}
        </>
      );
    } else if (
      secondaryLayer.type === SecondaryViewType.Alternatives &&
      store.alternative.data
    ) {
      return (
        <>
          {alternativeDisplay}
          {renderSecondLayer}
          {rendershoppingBag}
        </>
      );
    }
  }

  const handleFilterPackageSize = (value) => {
    setPage(1);
    setRowsPerPage(30);
    setPackageSizes(value);
  };

  const onInitSearchFavourite = () => {
    if (isDisplaySearchMedicineResult) {
      return;
    }
    medicationActions.setIsDisplaySearchMedicineResult(true);
    getMedicationResults({
      medicineQuery,
      isDiscounted,
      page,
      rowsPerPage,
      sortType,
      isChangeConcentration,
      isShowRegistered,
      groupCheckboxesSearch,
      memoizedIkNumber,
      isSVPatient,
      schein,
      patientId: patient?.id!,
      listSubstance,
      packageSizes,
    });
  };

  return (
    <>
      <Flex column className={className} ref={medKBVRef}>
        <Flex column auto className="outer-wrapper" gap={scaleSpace(4)}>
          <div className="wrapper">
            <Flex className="top" column gap={8}>
              <Flex
                className={`sl-checkboxes-search ${searchType ? 'sl-disabled-checkbox' : ''
                  }`}
                gap={16}
              >
                <BodyTextM>{t('SearchMedicationBox.searchIn')}: </BodyTextM>
                <Checkbox
                  className="sl-checkbox-item"
                  defaultChecked={groupCheckboxesSearch.isTradeName}
                  label={t('SearchMedicationBox.tradeName')}
                  onChange={(e) =>
                    handleChangeGroupCheckboxesSearch(e, 'isTradeName')
                  }
                  disabled={Boolean(searchType)}
                />
                <Checkbox
                  className="sl-checkbox-item"
                  defaultChecked={groupCheckboxesSearch.isSubstanceName}
                  label={t('SearchMedicationBox.substanceName')}
                  onChange={(e) =>
                    handleChangeGroupCheckboxesSearch(e, 'isSubstanceName')
                  }
                  disabled={Boolean(searchType)}
                />
                <Checkbox
                  className="sl-checkbox-item"
                  defaultChecked={groupCheckboxesSearch.isManufaturer}
                  label={t('SearchMedicationBox.manufacturer')}
                  onChange={(e) =>
                    handleChangeGroupCheckboxesSearch(e, 'isManufaturer')
                  }
                  disabled={Boolean(searchType)}
                />
              </Flex>
              <Flex className="search-bar">
                <SearchMedicationBox
                  searchType={searchType}
                  medicineQuery={medicineQuery}
                  listSubstance={listSubstance}
                  handleSearch={handleSearch}
                  handleSetSearchType={(searchType) => {
                    setSearchType(searchType);
                  }}
                  clearSearchBox={clearSearchBox}
                  setListSubstance={setListSubstance}
                  onInitSearchFavourite={onInitSearchFavourite}
                  isShowDelete={isDisplaySearchMedicineResult}
                />
                <Tooltip content={t('SearchMedicationBox.readhand')}>
                  <Button
                    intent={Intent.SUCCESS}
                    minimal
                    outlined
                    className="button"
                    icon={<Svg src={RedHandIcon} />}
                    onClick={() => {
                      setShowLettersDialog(true);
                      setHandLettersType('red');
                      setFilterHandLetters(true);
                      getRedHandLetters.mutate({});
                    }}
                  />
                </Tooltip>
                <Tooltip content={t('SearchMedicationBox.iww')}>
                  <Button
                    intent={Intent.PRIMARY}
                    className="button"
                    disabled={!store.iwwRegulation.isExist}
                    onClick={handleClickIwwBtn}
                  >
                    {IWW}
                  </Button>
                </Tooltip>
                <Tooltip content={t('SearchMedicationBox.amrl')}>
                  <Button
                    intent={Intent.PRIMARY}
                    className="secondary button"
                    onClick={() => setIsOpenGeneralGBA(true)}
                  >
                    {t('GeneralGBA.AM-RL')}
                  </Button>
                </Tooltip>
                <IWWRegulationsDialog
                  isOpen={isShowIwwRegulations}
                  handleCloseDialog={() =>
                    setShowIwwRegulations(!isShowIwwRegulations)
                  }
                  onClickMedicationLink={handleClickMedicationLink}
                />
                <Tooltip content={t('SearchMedicationBox.iconList')}>
                  <Button
                    intent={Intent.PRIMARY}
                    className="secondary button shopping-bag"
                    onClick={() => medicationActions.setShowShoppingBag(true)}
                  >
                    <Svg src={IconList} />
                    {renderMedicineInShoppingBagItemCount}
                  </Button>
                </Tooltip>
              </Flex>
              {isShowWarningFavourite && (
                <Callout
                  className="sl-callout-discounted"
                  intent={Intent.WARNING}
                  icon={
                    <Svg
                      src={warnInfoCircleIcon}
                      style={{ display: 'inline-flex', marginRight: 8 }}
                    />
                  }
                >
                  {t('SearchResults.noFavouriteAvailable')}
                </Callout>
              )}
              {isShowWarningDiscount && (
                <Callout
                  className="sl-callout-discounted"
                  intent={Intent.WARNING}
                  icon={
                    <Svg
                      src={warnInfoCircleIcon}
                      style={{ display: 'inline-flex', marginRight: 8 }}
                    />
                  }
                >
                  {t('SearchResults.noDiscountedProductsAvailable')}
                </Callout>
              )}
              {isDisplaySearchMedicineResult && (
                <Flex justify="space-between" align="center" gap={16}>
                  <Flex gap={16} align="center" className="sl-search-result">
                    <Switch
                      checked={store.isFavourite}
                      onChange={handleToggleIsFavourite}
                      disabled={store.isDisableFavourite}
                      label={t('SearchResults.onlyShowFavouriteMedications')}
                    />
                    <Switch
                      checked={isDiscounted}
                      onChange={handleToggleIsDiscounted}
                      label={t('SearchResults.onlyShowDiscountedProducts')}
                    />
                    <Switch
                      defaultChecked={isShowRegistered}
                      onChange={debounce((e) => {
                        setIsShowRegistered(e.target?.checked);
                      }, 300)}
                      label={t('SearchResults.onlyShowRegisteredProducts')}
                    />
                    <FilterPackageSize
                      value={packageSizes}
                      onChange={handleFilterPackageSize}
                    />
                  </Flex>
                </Flex>
              )}
            </Flex>
            {isDisplaySearchMedicineResult && (
              <MedicationTable
                maxHeightTable={maxHeightTable}
                medicationResults={store.medicationResults!}
                isLoading={store.isLoading}
                isLoadingSorting={store.isLoadingSorting}
                page={page}
                rowsPerPage={rowsPerPage}
                setPage={setPage}
                setRowsPerPage={setRowsPerPage}
                handleSetCurrentMedicine={(medicine) => {
                  handleSetPzn(medicine.pzn);
                }}
                sortType={sortType}
                handleSort={handleSort}
                currentPzn={pzn}
                selectAllRowsItem={selectAllRowsItem}
                handleAddToShoppingBag={handleAddToShoppingBag}
                handleOpenMedicationDialog={handleOpenMedicationDialog}
                handleRedHandLetters={handleRedHandLetters}
                handleBlueHandLetters={handleBlueHandLetters}
                callbackMarkFavourite={() => {
                  if (store.isFavourite) {
                    triggerSearch();
                  }
                }}
              />
            )}
          </div>
          {MedicationTabs}
        </Flex>
      </Flex>
      {renderSecondLayer}
      {rendershoppingBag}
      {secondaryLayer?.type === SecondaryViewType.Alternatives &&
        alternativeDisplay}
      {medicationContext.showPrintReview && (
        <MedicationPrintPreview
          clearSearchBox={clearSearchBox}
          bsnr={store.isStatistics ? bsnr : undefined}
          medicineType="kbv"
        />
      )}
      <RemoveShoppingBagDialog
        show={isRemoveSBDialog}
        isLoading={isLoadingRemoveSBDialog}
        onCancel={onCancelRemoveSBDialog}
        onConfirmRemove={onConfirmRemoveSBDialog}
      />

      {store.isOpenRefillDialog && (
        <RefillMedicationView
          isOpen={store.isOpenRefillDialog}
          handleClose={handleCloseRefillView}
          ikNumber={`${memoizedIkNumber}`}
          patient={patient}
          selectedContractDoctor={selectedContractDoctor}
        />
      )}

      {store.isOpenChangeMedicationPackageSizeDialog && (
        <ChangePackageSizeView
          isOpen={store.isOpenChangeMedicationPackageSizeDialog}
          handleClose={() => {
            medicationActions.setIsOpenChangeMedicationPackageSizeDialog(false);
            medicationActions.resetPackageSizeStore();
          }}
          isSVPatient={isSVPatient}
          ikNumber={memoizedIkNumber}
          contractId={schein.activatedSchein?.hzvContractId}
          isPrivateSchein={checkIsPrivateSchein(schein.activatedSchein)}
        />
      )}

      {Boolean(medicationContext.secondaryInfo) && (
        <SecondaryInfoDialog
          isOpen={Boolean(medicationContext.secondaryInfo)}
          handleCloseDialog={() =>
            medicationContext.setSecondaryInfo(undefined)
          }
          medicine={medicationContext.secondaryInfo}
        />
      )}

      <NoPackageSizeDialog />
      <NoSubtanceDialog />
      {openBmpMedicationDialog && (
        <MedicationDialog
          isBMPHintTextModule
          freetextPrescriptionName={store.medicineQuery?.keyword}
          isOpen={openBmpMedicationDialog}
          close={handleCloseMedicationDialog}
          saveMedication={handleSaveMedication}
          units={medicationContext.units}
          forms={medicationContext.forms}
          reasons={medicationContext.reasons}
          hints={medicationContext.hints}
          addHint={medicationContext.addHint}
          addReason={medicationContext.addReason}
        />
      )}
      <GeneralGBA isOpen={isOpenGeneralGBA} onClose={handleOnCloseGeneralGBA} />
      {isShowPopupUpdateStatus && (
        <InfoConfirmDialog
          type="primary"
          isOpen={isShowPopupUpdateStatus}
          title={t('titlePopupUpdateStatus')}
          cancelText={tButtonActions('okText')}
          isShowIconTitle
          isCloseButtonShown={false}
          isConfirmButtonShown={false}
          stopPropagation
          onClose={() => {
            setIsShowPopupUpdateStatus(false);
          }}
        >
          {t('descPopupUpdateStatus')}
        </InfoConfirmDialog>
      )}
      {isShowLettersDialog && (
        <LettersDialog
          redHandLetters={redHandLetters}
          blueHandLetters={blueHandLetters}
          handLettersType={handLettersType}
          hasFilter={hasFilterHandLetters}
          isLoading={
            getRedHandLetters.isPending || getBlueHandLetters.isPending
          }
          onClose={() => {
            setRedHandLetters(undefined);
            setBlueHandLetters(undefined);
            setShowLettersDialog(false);
            setFilterHandLetters(false);
          }}
        />
      )}
    </>
  );
};

export default memo(
  I18n.withTranslation(MedicationKBV, {
    namespace: 'Medication',
  })
);
