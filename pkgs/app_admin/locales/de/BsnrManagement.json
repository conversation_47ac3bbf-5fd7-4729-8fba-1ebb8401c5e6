{"BsnrOverview": {"header": "Betriebsstättenübersicht", "createBsnr": "Neue Praxis"}, "BsnrTable": {"bsnr": "BSNR", "city": "Stadt", "billingDoctors": "Arzt/ Ärzte", "rvsa": "RVSA?", "editBsnr": "BSNR bearbeiten", "editNbsnr": "NBSNR bearbeiten", "editRvsaCertificate": "RVSA bearbeiten", "createNbsnr": "<PERSON>eue Praxis (NBSNR)", "noData": "Keine BSNR vorhanden", "deactivate": "Entfernen"}, "DeactivateBsnr": {"title": "<PERSON><PERSON>cht<PERSON> Sie die Praxis wirklich entfernen?", "message": "Durch diese Aktion wird die Praxis entfernt und steht nicht mehr zur Auswahl. Bestehende Patientendokumentationen bleiben davon unberührt. Die Aktion kann nur von einem Administrator des Softwareherstellers rückgängig gemacht werden.", "deactivateSuccess": "<PERSON><PERSON><PERSON>", "deactivateError": "Löschen der Praxis fehlgeschlagen"}, "CreateBsnr": {"createBsnrTitle": "Neue Praxis", "createNBsnrTitle": "<PERSON><PERSON>e Praxis (NBSNR) erstellen für {{ bsnrId }}", "editBsnrTitle": "BSNR bearbeiten", "editNBsnrTitle": "NBSNR bearbeiten für {{ bsnrId }}", "editRvsaTitle": "RVSA bearbeiten", "practiceDetailsLb": "Praxisdetails", "rvsaCertificateLb": "RVSA Zertifikat", "othersLb": "<PERSON><PERSON>", "bsnrCreatedToast": "BSNR wurde erstellt", "nBsnrCreatedToast": "NBSNR wurde erstellt", "bsnrSavedToast": "BSNR wurde aktualisiert", "nBsnrSavedToast": "NBSNR wurde aktualisiert", "rvsaSavedToast": "RVSA wurde aktualisiert", "PracticeDetails": {"bsnr": "BSNR", "NBsnr": "NBSNR", "bsnrName": "Praxisname", "NBsnrName": "(N)BSNR Name", "billingDoctors": "Abrechnende Ärzte", "uv": "KV-Bereich", "street": "Straße", "number": "<PERSON><PERSON><PERSON>", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "city": "Stadt", "country": "Land", "phone": "Telefon", "fax": "Fax", "email": "E-Mail", "subjectArea": "Fachbereich/e", "facilityType": "Pflegeheimeinrichtung", "practice": "<PERSON><PERSON><PERSON>", "hospital": "Krankenhaus", "practiceStamp": "Praxisstempel"}, "RvsaCertificate": {"isRequireLb": "Werden zertifikatspflichtige Laborleistungen in der BSNR abgerechnet (RVSA)?", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "areRegentsUsedIn": "Werden in der Betriebsstätte im Rahmen einer patientennahen Sofortdiagnostik Unit-Use-Reagenzien verwendet?", "yesExclusively": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yesPartially": "<PERSON>a, teilweise", "labParameter": "Labor Parameter", "certificateStatus": "Zertif<PERSON>t vorhanden?", "gnr": "GNR", "validity": "Gültigkeit", "valid": "<PERSON><PERSON><PERSON><PERSON>", "inValid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceType": "Gerätetyp", "manufacturer": "<PERSON><PERSON><PERSON>", "addDeviceType": "Ein weiteres Gerät hinzufügen", "rvsaCertificateStatus": "Existiert für die ausgewählten Analyten ein Zertifikat?", "pnSDAnalyse": "pnSD/uu-Analyse", "searchPlaceholder": "Suche nach Analyt oder GNR", "validFromTooltip": "Gültig seit: {{ validFrom }}", "validTillTooltip": "Gültig bis: {{ validTill }}", "highlightServiceCodeNote": "Im aktuellen Quartal dokumentierte Leistungsziffern werden hervorgehoben.", "btnSetCertificateStatusPnSd": "Auswahl pnSd/uu", "btnSetCertificateStatusYes": "Auswahl nicht vorharden"}, "Others": {"otherTiApplications": "Weitere TI- Anwendungen", "ePAStufe1": "ePA Stufe 1", "eRezept": "eRezept", "epa": "ePA", "none": "Keine ePA", "ePAStufe2": "ePA Stufe 2", "nfdm": "NFDM", "emp": "E-MP", "kim": "KIM", "eau": "eAU", "eartzbrief": "eArztbrief", "kartenterminal": "Kartenterminal", "smcb": "SMC-B", "ehba": "eHBA", "ePAStufe3": "ePA Stufe 3"}, "BankInformation": {"BankInformationSection": "Zahlungsinformationen", "bankName": "Name der Bank", "accountHolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iban": "IBAN", "bic": "BIC", "AddNewBankInfo": "Bankkonto hinzufügen"}}}