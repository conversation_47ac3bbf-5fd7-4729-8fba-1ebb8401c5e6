{"BsnrOverview": {"header": "BSNR overview", "createBsnr": "Create BSNR"}, "BsnrTable": {"bsnr": "BSNR", "city": "City", "billingDoctors": "Billing Doctors", "rvsa": "RVSA?", "editBsnr": "Edit BSNR", "editNbsnr": "Edit NBSNR", "editRvsaCertificate": "Edit RVSA certificate", "createNbsnr": "Create NBSNR", "noData": "No BSNR created yet", "deactivate": "Deactivate"}, "DeactivateBsnr": {"title": "Are you sure you want to delete this practice?", "message": "This action will delete the practice and it will no longer be available for selection. Existing patient documentation will remain unaffected.", "deactivateSuccess": "Practice deleted", "deactivateError": "Failed to delete practice"}, "CreateBsnr": {"createBsnrTitle": "Create BSNR", "createNBsnrTitle": "Create NBSNR for {{ bsnrId }}", "editBsnrTitle": "Edit BSNR", "editNBsnrTitle": "Edit NBSNR for {{ bsnrId }}", "editRvsaTitle": "Edit RVSA certificate", "practiceDetailsLb": "Practice details", "rvsaCertificateLb": "RVSA certificate", "othersLb": "Others", "bsnrCreatedToast": "BSNR created", "nBsnrCreatedToast": "NBSNR created", "bsnrSavedToast": "BSNR updated", "nBsnrSavedToast": "NBSNR updated", "rvsaSavedToast": "RVSA updated", "PracticeDetails": {"bsnr": "BSNR", "NBsnr": "NBSNR", "bsnrName": "BSNR Name", "NBsnrName": "NBSNR Name", "billingDoctors": "Billing Doctors", "uv": "UV", "subjectArea": "subject area", "street": "Street", "number": "Number", "postalCode": "Postal Code", "city": "City", "country": "Country", "phone": "Phone", "fax": "Fax", "email": "E-mail", "facilityType": "Facility Type", "practice": "Practice", "hospital": "Hospital", "practiceStamp": "Practice Stamp"}, "RvsaCertificate": {"isRequireLb": "Are laboratory services that require a certificate billed in the BSNR?", "yes": "Yes", "no": "No", "areRegentsUsedIn": "Are reagents used in the BSNR as part of a patient pathways immediate diagnosis?", "yesExclusively": "Yes, exclusively", "yesPartially": "Yes, partially", "labParameter": "Lab Parameter", "certificateStatus": "Certificate available?", "gnr": "GNR", "validity": "Validity", "valid": "<PERSON><PERSON>", "inValid": "Invalid", "deviceType": "Device Type", "manufacturer": "Manufacturer", "addDeviceType": "Add device type", "rvsaCertificateStatus": "Is there a certificate for this lab parameter in this BSNR?", "pnSDAnalyse": "pnSD/uu-Analyse", "searchPlaceholder": "Search by lab parameter or ID", "validFromTooltip": "Valid from: {{ validFrom }}", "validTillTooltip": "Valid till: {{ validTill }}", "highlightServiceCodeNote": "Service codes documented in current quarter are highlighted.", "btnSetCertificateStatusPnSd": "Set as pnSd/uu", "btnSetCertificateStatusYes": "Set as nicht vorharden"}, "Others": {"otherTiApplications": "Other TI applications", "ePAStufe1": "ePA Stufe 1", "eRezept": "ePrescription", "epa": "ePA", "none": "None", "ePAStufe2": "ePA Stufe 2", "nfdm": "NFDM", "emp": "E-MP", "kim": "KIM", "eau": "eAU", "eartzbrief": "eDoctor Letter", "kartenterminal": "Kartenterminal", "smcb": "SMC-B", "ehba": "eHBA", "ePAStufe3": "ePA Stufe 3"}, "BankInformation": {"BankInformationSection": "Bank information", "bankName": "bank name", "accountHolder": "account holder", "iban": "IBAN", "bic": "bic", "AddNewBankInfo": "Add bank account"}}}