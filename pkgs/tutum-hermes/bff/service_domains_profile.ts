/* eslint-disable */
// This code was autogenerated from service/domains/profile.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as admin_bff from "./app_admin"
import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"


// Type definitions
		export interface GetByIdsRequest {
				originalIds: Array<string>
		}
	

		export interface GetByLanrIDRequest {
				lanr: string
		}
	

		export interface GetByHzvIDRequest {
				havgId: string
		}
	

		export interface GetByMediIDRequest {
				mediId: string
		}
	

		export interface GetByBsnrIdRequest {
				bsnrId: string
		}
	

		export interface Contract {
				contractId: string
				startDate: number
				endDate?: number
		}
	

		export interface EmployeeProfileResponse {
				id: string
				fullName: string
				firstName: string
				lastName: string
				title?: string
				dob?: number
				salutation?: patient_profile_common.Salutation
				email?: string
				phone?: string
				address?: string
				lanr?: string
				bsnr: string
				havgId?: string
				havgVpId?: string
				mediverbundId?: string
				mediverbundVpId?: string
				areaOfExpertise?: Array<string>
				mobilePhone: string
				okv?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				zipCode: string
				street: string
				fax: string
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				jobDescription?: string
				markAsBillingDoctor: boolean
				bsnrId: string
				employeeProfileId?: string
				pseudoLanr?: string
				teamNumbers?: Array<string>
				doctorStamp: string
				bsnrCity: string
				bsnrPracticeStamp: string
				bankInformations: Array<common.BankInformation>
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				bsnrName?: string
				bsnrStreet: string
				bsnrNumber: string
				bsnrPostCode: string
				bsnrPhoneNumber: string
				bsnrFaxNumber: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				externalId: string
				bsnrFacilityType: string
				deviceId?: string
				userName: string
				orgId: string
				hpmEndpoint: string
				createdDate: number
				status: common.EmployeeStatus
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				isDoctor: boolean
				bsnrs: Array<string>
				bsnrIds: Array<string>
				eHKSType?: common.EHKSType
		}
	

		export interface EmployeeProfilesResponse {
				profiles: Array<EmployeeProfileResponse>
		}
	

		export interface EmployeeProfileDeleteResponse {
				result: number
		}
	

		export interface EmployeeProfileDeleteRequest {
				originalId: string
		}
	

		export interface EmployeeProfileGetRequest {
				originalId: string
				bsnrId?: string
		}
	

		export interface EmployeeProfileRequest {
				originalId: string
				firstName: string
				lastName: string
				title?: string
				dob?: number
				salutation?: patient_profile_common.Salutation
				email?: string
				phone?: string
				address?: string
				lanr?: string
				bsnr: string
				havgId?: string
				havgVpId?: string
				mediverbundId?: string
				mediverbundVpId?: string
				areaOfExpertise?: Array<string>
				mobilePhone: string
				okv?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				jobDescription?: string
				markAsBillingDoctor: boolean
				bsnrId?: string
				pseudoLanr?: string
				teamNumbers?: Array<string>
				doctorStamp: string
				bsnrPracticeStamp: string
				bankInformations: Array<common.BankInformation>
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				bsnrName?: string
				bsnrStreet: string
				bsnrNumber: string
				bsnrPostCode: string
				password: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				deviceId?: string
				userName: string
				externalId: string
				hpmEndpoint: string
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				bsnrs: Array<string>
				bsnrIds?: Array<string>
				eHKSType?: common.EHKSType
				isDoctor: boolean
		}
	

		export interface GetAllInitialResponse {
				data: Array<string>
		}
	

		export interface GetEmployeeByExternalIdRequest {
				externalId: string
		}
	

		export interface UpdateDeviceRequest {
				deviceId: string
		}
	

		export interface GetEmployeeByDeviceIdRequest {
				deviceId: string
		}
	

		export interface GetByCareProviderIdRequest {
				careProviderId: string
		}
	

		export interface SearchEmployeeByNameRequest {
				name: string
				careProviderId: string
				paginationRequest: common.PaginationRequest
		}
	

		export interface SearchEmployeeByNameResponse {
				profiles: Array<EmployeeProfileResponse>
				paginationResponse: common.PaginationResponse
		}
	

		export interface UpdateEmployeeStatusRequest {
				externalId: string
				status: common.EmployeeStatus
		}
	

		export interface ResetEmployeePasswordRequest {
				externalId: string
		}
	

		export interface ResetEmployeePasswordResponse {
				password: string
		}
	

		export interface ResetEmployee2FARequest {
				externalId: string
		}
	

		export interface GetByIdRequest {
				patientId: string
		}
	

		export interface PatientProfileCreateRequest {
				patientId: string
				title: string
				lastName: string
				firstName: string
				dateOfBirth?: number
				gender: patient_profile_common.Gender
				street: string
				houseNumber: string
				postCode: string
				cityState: string
				country: string
				primaryContact: string
				secondaryContact: string
				contactPerson: string
				email: string
				intendWords: string
		}
	

		export interface PatientProfileUpdateRequest {
				patientId: string
				title: string
				firstName: string
				lastName: string
				dateOfBirth?: number
				gender: patient_profile_common.Gender
				street: string
				houseNumber: string
				postCode: string
				cityState: string
				country: string
				primaryContact: string
				secondaryContact: string
				contactPerson: string
				email: string
				intendWords: string
				additionalName: string
				job: string
				company: string
				previousDoctor: string
				additionalAddress: string
				postfach: string
				plzPostfach: string
				stadtPostfach: string
				isEmployee: IsEmploymentAnswer
				jobStatus: string
				workingHourInWeek: number
				workActivity1: WorkActivity1
				workActivity2: WorkActivity2
				employmentInformationLastUpdated: number
				specialProblemAtWork: string
		}
	

		export interface UpdatePatientMedicalDataRequest {
				patientId: string
				patientMedicalData: patient_profile_common.PatientMedicalData
		}
	

		export interface UpdatePatientMedicalDataResponse {
				medicalDataHistoryId: string
				patientMedicalData: patient_profile_common.PatientMedicalData
		}
	

		export interface PatientProfile {
				id: string
				firstName: string
				lastName: string
				dateOfBirth?: number
				hpmInformation: Array<HpmInformation>
				patientMedicalData: patient_profile_common.PatientMedicalData
				patientInfo: patient_profile_common.PatientInfo
				employmentInfoUpdatedAt?: number
				medicalDataUpdatedAt?: number
		}
	

		export interface PatientResponse {
				id: string
				accountId: string
				careProviderId: string
		}
	

		export interface HpmInformation {
				checkedDate: number
				contractId: string
				status: HpmInformationStatus
		}
	

		export interface UpdateHpmInformationRequest {
				patientId: string
				hpmInformation: Array<HpmInformation>
		}
	

		export interface GetProfileByIdsRequest {
				ids: Array<string>
		}
	

		export interface GetProfileByIdsResponse {
				patientProfiles: Array<PatientProfile>
		}
	

		export interface EventHealthInsuranceChange {
				insuranceNumber: string
				ikNumber: number
				patientId: string
		}
	


// enum definitions
    export enum WorkActivity1 {
        Physical = "Physical",
        Mental = "Mental",
	}

    export enum WorkActivity2 {
        Standing = "Standing",
        Sitting = "Sitting",
	}

    export enum IsEmploymentAnswer {
        Yes = "Yes",
        No = "No",
	}

    export enum HpmInformationStatus {
        HpmInformationStatus_Active = "Active",
        HpmInformationStatus_InActive = "InActive",
        HpmInformationStatus_Error = "Error",
	}


// event definition constant ----------------------------------------
       	const EVENT_HealthInsuranceChange = "api.service.domains.ServiceDomainsProfile.HealthInsuranceChange";

// Define bff event listener  -----------------------------------------------------------------------------------------
        export function useListenHealthInsuranceChange(handler: (data: EventHealthInsuranceChange) => void): void {
			const [response, setResponse] = useState<EventHealthInsuranceChange>();
			
			const _listener = useCallback((event: CustomEvent) => {
				handleEventMessage(event.detail, setResponse);
			}, []);
			
			useEffect(() => {
				if (response) {
					handler(response);
				}
			}, [response]);

			useEffect(() => {
				window.addEventListener(EVENT_HealthInsuranceChange, _listener);
				return () => window.removeEventListener(EVENT_HealthInsuranceChange, _listener);
			}, []);
        }

