/* eslint-disable */
// This code was autogenerated from app/profile/profile_app.proto, do not edit.
import { useCallback, useEffect, useState } from 'react';

import { handleEventMessage } from '@tutum/hermes/websocket';

import * as admin_bff from "./app_admin"
import * as bsnr_common from "./bsnr_common"
import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"


// Type definitions
		export interface EmployeeProfileResponse {
				id: string
				firstName: string
				lastName: string
				title: string
				salutation?: patient_profile_common.Salutation
				dob?: number
				phone?: string
				email?: string
				address?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				bsnr: string
				havgId: string
				mediId: string
				lanr: string
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				markAsBillingDoctor: boolean
				pseudoLanr?: string
				teamNumbers?: Array<string>
				doctorStamp: string
				bsnrCity: string
				bsnrPracticeStamp: string
				bankInformations: Array<common.BankInformation>
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				bsnrName?: string
				bsnrStreet: string
				bsnrNumber: string
				bsnrPostCode: string
				bsnrPhoneNumber: string
				bsnrFaxNumber: string
				areaOfExpertise?: Array<string>
				orgId: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				havgVpId: string
				mediVpId: string
				bsnrFacilityType: string
				hpmEndpoint: string
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				userName: string
				deviceId: string
				isDoctor: boolean
				jobDescription: string
				okv: string
				bsnrIds: Array<string>
				bsnrs: Array<string>
				bsnrId?: string
				eHKSType?: common.EHKSType
		}
	

		export interface MyEmployeeProfileResponse {
				id: string
				firstName: string
				lastName: string
				title: string
				salutation?: patient_profile_common.Salutation
				dob?: number
				phone?: string
				email?: string
				address?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				bsnr: string
				havgId: string
				mediId: string
				lanr: string
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				markAsBillingDoctor: boolean
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				jobDescription?: string
				bsnrName?: string
				doctorStamp: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				deviceId?: string
				externalId: string
				userName: string
				orgId: string
				hpmEndpoint: string
				bankInformations: Array<common.BankInformation>
				areaOfExpertise?: Array<string>
				bsnrId?: string
				bsnrs: Array<string>
				bsnrIds?: Array<string>
				eHKSType?: common.EHKSType
				isDoctor: boolean
		}
	

		export interface Practice {
				bsnr: string
				name: string
				endDate: number
				startDate: number
		}
	

		export interface EmployeeProfilesResponse {
				profiles: Array<EmployeeProfileResponse>
		}
	

		export interface GetByIdsRequest {
				originalIds: Array<string>
		}
	

		export interface GetByBsnrRequest {
				bsnrId: string
		}
	

		export interface GetByLanrIDRequest {
				lanr: string
		}
	

		export interface GetByHzvIDRequest {
				havgId: string
		}
	

		export interface GetByMediIDRequest {
				mediId: string
		}
	

		export interface GetAllInitialResponse {
				data: Array<string>
		}
	

		export interface CareProvider {
				id: string
				name: string
				bsnrs: Array<bsnr_common.BSNR>
		}
	

		export interface GetListBsnrOfEmployeeResponse {
				careProviders: Array<CareProvider>
		}
	


// enum definitions

// event definition constant ----------------------------------------

// Define bff event listener  -----------------------------------------------------------------------------------------

