/* eslint-disable */
// This code was autogenerated from app/profile/profile_app.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as admin_bff from "./app_admin"
import * as bsnr_common from "./bsnr_common"
import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"

import { fetchWithHeaders, CustomUseQueryOptions, ErrorType, ResponseType, CustomRequestInit } from "./api_client";


// Type definitions
		export interface EmployeeProfileResponse {
				id: string
				firstName: string
				lastName: string
				title: string
				salutation?: patient_profile_common.Salutation
				dob?: number
				phone?: string
				email?: string
				address?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				bsnr: string
				havgId: string
				mediId: string
				lanr: string
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				markAsBillingDoctor: boolean
				pseudoLanr?: string
				teamNumbers?: Array<string>
				doctorStamp: string
				bsnrCity: string
				bsnrPracticeStamp: string
				bankInformations: Array<common.BankInformation>
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				bsnrName?: string
				bsnrStreet: string
				bsnrNumber: string
				bsnrPostCode: string
				bsnrPhoneNumber: string
				bsnrFaxNumber: string
				areaOfExpertise?: Array<string>
				orgId: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				havgVpId: string
				mediVpId: string
				bsnrFacilityType: string
				hpmEndpoint: string
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				userName: string
				deviceId: string
				isDoctor: boolean
				jobDescription: string
				okv: string
				bsnrIds: Array<string>
				bsnrs: Array<string>
				bsnrId?: string
				eHKSType?: common.EHKSType
		}
	

		export interface MyEmployeeProfileResponse {
				id: string
				firstName: string
				lastName: string
				title: string
				salutation?: patient_profile_common.Salutation
				dob?: number
				phone?: string
				email?: string
				address?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				bsnr: string
				havgId: string
				mediId: string
				lanr: string
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				markAsBillingDoctor: boolean
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				jobDescription?: string
				bsnrName?: string
				doctorStamp: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				deviceId?: string
				externalId: string
				userName: string
				orgId: string
				hpmEndpoint: string
				bankInformations: Array<common.BankInformation>
				areaOfExpertise?: Array<string>
				bsnrId?: string
				bsnrs: Array<string>
				bsnrIds?: Array<string>
				eHKSType?: common.EHKSType
				isDoctor: boolean
		}
	

		export interface Practice {
				bsnr: string
				name: string
				endDate: number
				startDate: number
		}
	

		export interface EmployeeProfilesResponse {
				profiles: Array<EmployeeProfileResponse>
		}
	

		export interface GetByIdsRequest {
				originalIds: Array<string>
		}
	

		export interface GetByBsnrRequest {
				bsnrId: string
		}
	

		export interface GetByLanrIDRequest {
				lanr: string
		}
	

		export interface GetByHzvIDRequest {
				havgId: string
		}
	

		export interface GetByMediIDRequest {
				mediId: string
		}
	

		export interface GetAllInitialResponse {
				data: Array<string>
		}
	

		export interface CareProvider {
				id: string
				name: string
				bsnrs: Array<bsnr_common.BSNR>
		}
	

		export interface GetListBsnrOfEmployeeResponse {
				careProviders: Array<CareProvider>
		}
	


// enum definitions

// method name convention const
		export const EVENT_GetAllInitial = "api.app.profile.ProfileBff.GetAllInitial";
		export const EVENT_GetAllInitial_Response = "api.app.profile.ProfileBff.GetAllInitial.Response";
		export const EVENT_getMyEmployeeProfile = "api.app.profile.ProfileBff.GetMyEmployeeProfile";
		export const EVENT_getMyEmployeeProfile_Response = "api.app.profile.ProfileBff.GetMyEmployeeProfile.Response";
		export const EVENT_getEmployeeProfileByIds = "api.app.profile.ProfileBff.GetEmployeeProfileByIds";
		export const EVENT_getEmployeeProfileByIds_Response = "api.app.profile.ProfileBff.GetEmployeeProfileByIds.Response";
		export const EVENT_getEmployeeProfilesByBsnrId = "api.app.profile.ProfileBff.GetEmployeeProfilesByBsnrId";
		export const EVENT_getEmployeeProfilesByBsnrId_Response = "api.app.profile.ProfileBff.GetEmployeeProfilesByBsnrId.Response";
		export const EVENT_getByLanrID = "api.app.profile.ProfileBff.GetByLanrID";
		export const EVENT_getByLanrID_Response = "api.app.profile.ProfileBff.GetByLanrID.Response";
		export const EVENT_getByHzvID = "api.app.profile.ProfileBff.GetByHzvID";
		export const EVENT_getByHzvID_Response = "api.app.profile.ProfileBff.GetByHzvID.Response";
		export const EVENT_getByMediID = "api.app.profile.ProfileBff.GetByMediID";
		export const EVENT_getByMediID_Response = "api.app.profile.ProfileBff.GetByMediID.Response";
		export const EVENT_getEmployeeByIds = "api.app.profile.ProfileBff.GetEmployeeByIds";
		export const EVENT_getEmployeeByIds_Response = "api.app.profile.ProfileBff.GetEmployeeByIds.Response";
		export const EVENT_GetListBsnrOfEmployee = "api.app.profile.ProfileBff.GetListBsnrOfEmployee";
		export const EVENT_GetListBsnrOfEmployee_Response = "api.app.profile.ProfileBff.GetListBsnrOfEmployee.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetAllInitial = "/api/app/profile/getAllInitial";
        export const LEGACY_TOPIC_GetMyEmployeeProfile = "/api/app/profile/getMyEmployeeProfile";
        export const LEGACY_TOPIC_GetEmployeeProfileByIds = "/api/app/profile/getEmployeeProfileByIds";
        export const LEGACY_TOPIC_GetEmployeeProfilesByBsnrId = "/api/app/profile/getEmployeeProfilesByBsnrId";
        export const LEGACY_TOPIC_GetByLanrID = "/api/app/profile/getByLanrID";
        export const LEGACY_TOPIC_GetByHzvID = "/api/app/profile/getByHzvID";
        export const LEGACY_TOPIC_GetByMediID = "/api/app/profile/getByMediID";
        export const LEGACY_TOPIC_GetEmployeeByIds = "/api/app/profile/getEmployeeByIds";
        export const LEGACY_TOPIC_GetListBsnrOfEmployee = "/api/app/profile/getListBsnrOfEmployee";


// Define action methods and their listener -----------------------------------------------------------------
			export async function getAllInitial(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetAllInitialResponse>("POST", LEGACY_TOPIC_GetAllInitial, { init })
			}

			export function useQueryGetAllInitial<TransformedType =GetAllInitialResponse>(ops?: CustomUseQueryOptions<ResponseType<GetAllInitialResponse>, TransformedType>) {
                return useQuery<ResponseType<GetAllInitialResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetAllInitial],
					queryFn: async ({ signal }) => await getAllInitial({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetAllInitial(opts?: UseMutationOptions<ResponseType<GetAllInitialResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getAllInitial(),
						retry: false,
						...opts
                });
            }
    
			export async function getMyEmployeeProfile(init?: CustomRequestInit) {
				return await fetchWithHeaders<MyEmployeeProfileResponse>("POST", LEGACY_TOPIC_GetMyEmployeeProfile, { init })
			}

			export function useQueryGetMyEmployeeProfile<TransformedType =MyEmployeeProfileResponse>(ops?: CustomUseQueryOptions<ResponseType<MyEmployeeProfileResponse>, TransformedType>) {
                return useQuery<ResponseType<MyEmployeeProfileResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getMyEmployeeProfile],
					queryFn: async ({ signal }) => await getMyEmployeeProfile({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetMyEmployeeProfile(opts?: UseMutationOptions<ResponseType<MyEmployeeProfileResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getMyEmployeeProfile(),
						retry: false,
						...opts
                });
            }
    
			export async function getEmployeeProfileByIds(request: GetByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<EmployeeProfilesResponse>("POST", LEGACY_TOPIC_GetEmployeeProfileByIds, { init , request})
			}

			export function useQueryGetEmployeeProfileByIds<TransformedType =EmployeeProfilesResponse>(payload: GetByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<EmployeeProfilesResponse>, TransformedType>) {
                return useQuery<ResponseType<EmployeeProfilesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getEmployeeProfileByIds, payload],
					queryFn: async ({ signal }) => await getEmployeeProfileByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetEmployeeProfileByIds(opts?: UseMutationOptions<ResponseType<EmployeeProfilesResponse>, ErrorType,GetByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getEmployeeProfileByIds(request),
						retry: false,
						...opts
                });
            }
    
			export async function getEmployeeProfilesByBsnrId(request: GetByBsnrRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<EmployeeProfilesResponse>("POST", LEGACY_TOPIC_GetEmployeeProfilesByBsnrId, { init , request})
			}

			export function useQueryGetEmployeeProfilesByBsnrId<TransformedType =EmployeeProfilesResponse>(payload: GetByBsnrRequest,ops?: CustomUseQueryOptions<ResponseType<EmployeeProfilesResponse>, TransformedType>) {
                return useQuery<ResponseType<EmployeeProfilesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getEmployeeProfilesByBsnrId, payload],
					queryFn: async ({ signal }) => await getEmployeeProfilesByBsnrId(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetEmployeeProfilesByBsnrId(opts?: UseMutationOptions<ResponseType<EmployeeProfilesResponse>, ErrorType,GetByBsnrRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getEmployeeProfilesByBsnrId(request),
						retry: false,
						...opts
                });
            }
    
			export async function getByLanrID(request: GetByLanrIDRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<EmployeeProfileResponse>("POST", LEGACY_TOPIC_GetByLanrID, { init , request})
			}

			export function useQueryGetByLanrID<TransformedType =EmployeeProfileResponse>(payload: GetByLanrIDRequest,ops?: CustomUseQueryOptions<ResponseType<EmployeeProfileResponse>, TransformedType>) {
                return useQuery<ResponseType<EmployeeProfileResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getByLanrID, payload],
					queryFn: async ({ signal }) => await getByLanrID(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetByLanrID(opts?: UseMutationOptions<ResponseType<EmployeeProfileResponse>, ErrorType,GetByLanrIDRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getByLanrID(request),
						retry: false,
						...opts
                });
            }
    
			export async function getByHzvID(request: GetByHzvIDRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<EmployeeProfileResponse>("POST", LEGACY_TOPIC_GetByHzvID, { init , request})
			}

			export function useQueryGetByHzvID<TransformedType =EmployeeProfileResponse>(payload: GetByHzvIDRequest,ops?: CustomUseQueryOptions<ResponseType<EmployeeProfileResponse>, TransformedType>) {
                return useQuery<ResponseType<EmployeeProfileResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getByHzvID, payload],
					queryFn: async ({ signal }) => await getByHzvID(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetByHzvID(opts?: UseMutationOptions<ResponseType<EmployeeProfileResponse>, ErrorType,GetByHzvIDRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getByHzvID(request),
						retry: false,
						...opts
                });
            }
    
			export async function getByMediID(request: GetByMediIDRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<EmployeeProfileResponse>("POST", LEGACY_TOPIC_GetByMediID, { init , request})
			}

			export function useQueryGetByMediID<TransformedType =EmployeeProfileResponse>(payload: GetByMediIDRequest,ops?: CustomUseQueryOptions<ResponseType<EmployeeProfileResponse>, TransformedType>) {
                return useQuery<ResponseType<EmployeeProfileResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getByMediID, payload],
					queryFn: async ({ signal }) => await getByMediID(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetByMediID(opts?: UseMutationOptions<ResponseType<EmployeeProfileResponse>, ErrorType,GetByMediIDRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getByMediID(request),
						retry: false,
						...opts
                });
            }
    
			export async function getEmployeeByIds(request: GetByIdsRequest,init?: CustomRequestInit) {
				return await fetchWithHeaders<EmployeeProfilesResponse>("POST", LEGACY_TOPIC_GetEmployeeByIds, { init , request})
			}

			export function useQueryGetEmployeeByIds<TransformedType =EmployeeProfilesResponse>(payload: GetByIdsRequest,ops?: CustomUseQueryOptions<ResponseType<EmployeeProfilesResponse>, TransformedType>) {
                return useQuery<ResponseType<EmployeeProfilesResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_getEmployeeByIds, payload],
					queryFn: async ({ signal }) => await getEmployeeByIds(payload, { signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetEmployeeByIds(opts?: UseMutationOptions<ResponseType<EmployeeProfilesResponse>, ErrorType,GetByIdsRequest, any>) {
                return useMutation({
						mutationFn: async(request) => await getEmployeeByIds(request),
						retry: false,
						...opts
                });
            }
    
			export async function getListBsnrOfEmployee(init?: CustomRequestInit) {
				return await fetchWithHeaders<GetListBsnrOfEmployeeResponse>("POST", LEGACY_TOPIC_GetListBsnrOfEmployee, { init })
			}

			export function useQueryGetListBsnrOfEmployee<TransformedType =GetListBsnrOfEmployeeResponse>(ops?: CustomUseQueryOptions<ResponseType<GetListBsnrOfEmployeeResponse>, TransformedType>) {
                return useQuery<ResponseType<GetListBsnrOfEmployeeResponse>, ErrorType, TransformedType, any>({
					queryKey: [EVENT_GetListBsnrOfEmployee],
					queryFn: async ({ signal }) => await getListBsnrOfEmployee({ signal }),
					retry: false,
					select: (response) => response.data as TransformedType,
					...ops			
                });
            }

			export function useMutationGetListBsnrOfEmployee(opts?: UseMutationOptions<ResponseType<GetListBsnrOfEmployeeResponse>, ErrorType,unknown, any>) {
                return useMutation({
						mutationFn: async() => await getListBsnrOfEmployee(),
						retry: false,
						...opts
                });
            }
    

