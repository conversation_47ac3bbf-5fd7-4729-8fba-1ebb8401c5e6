/* eslint-disable */
// This code was autogenerated from service/domains/profile.proto, do not edit.

//import * as common from './common'
import { useQuery, useMutation, UseMutationOptions } from '@tanstack/react-query';
import * as admin_bff from "./app_admin"
import * as common from "./common"
import * as patient_profile_common from "./patient_profile_common"



// Type definitions
		export interface GetByIdsRequest {
				originalIds: Array<string>
		}
	

		export interface GetByLanrIDRequest {
				lanr: string
		}
	

		export interface GetByHzvIDRequest {
				havgId: string
		}
	

		export interface GetByMediIDRequest {
				mediId: string
		}
	

		export interface GetByBsnrIdRequest {
				bsnrId: string
		}
	

		export interface Contract {
				contractId: string
				startDate: number
				endDate?: number
		}
	

		export interface EmployeeProfileResponse {
				id: string
				fullName: string
				firstName: string
				lastName: string
				title?: string
				dob?: number
				salutation?: patient_profile_common.Salutation
				email?: string
				phone?: string
				address?: string
				lanr?: string
				bsnr: string
				havgId?: string
				havgVpId?: string
				mediverbundId?: string
				mediverbundVpId?: string
				areaOfExpertise?: Array<string>
				mobilePhone: string
				okv?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				zipCode: string
				street: string
				fax: string
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				jobDescription?: string
				markAsBillingDoctor: boolean
				bsnrId: string
				employeeProfileId?: string
				pseudoLanr?: string
				teamNumbers?: Array<string>
				doctorStamp: string
				bsnrCity: string
				bsnrPracticeStamp: string
				bankInformations: Array<common.BankInformation>
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				bsnrName?: string
				bsnrStreet: string
				bsnrNumber: string
				bsnrPostCode: string
				bsnrPhoneNumber: string
				bsnrFaxNumber: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				externalId: string
				bsnrFacilityType: string
				deviceId?: string
				userName: string
				orgId: string
				hpmEndpoint: string
				createdDate: number
				status: common.EmployeeStatus
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				isDoctor: boolean
				bsnrs: Array<string>
				bsnrIds: Array<string>
				eHKSType?: common.EHKSType
		}
	

		export interface EmployeeProfilesResponse {
				profiles: Array<EmployeeProfileResponse>
		}
	

		export interface EmployeeProfileDeleteResponse {
				result: number
		}
	

		export interface EmployeeProfileDeleteRequest {
				originalId: string
		}
	

		export interface EmployeeProfileGetRequest {
				originalId: string
				bsnrId?: string
		}
	

		export interface EmployeeProfileRequest {
				originalId: string
				firstName: string
				lastName: string
				title?: string
				dob?: number
				salutation?: patient_profile_common.Salutation
				email?: string
				phone?: string
				address?: string
				lanr?: string
				bsnr: string
				havgId?: string
				havgVpId?: string
				mediverbundId?: string
				mediverbundVpId?: string
				areaOfExpertise?: Array<string>
				mobilePhone: string
				okv?: string
				hasHzvContracts: boolean
				hasFavContracts: boolean
				additionalName?: patient_profile_common.AdditionalName
				intendWord?: patient_profile_common.IntendWord
				initial: string
				dmpPrograms?: Array<string>
				jobDescription?: string
				markAsBillingDoctor: boolean
				bsnrId?: string
				pseudoLanr?: string
				teamNumbers?: Array<string>
				doctorStamp: string
				bsnrPracticeStamp: string
				bankInformations: Array<common.BankInformation>
				markAsEmployedDoctor: boolean
				responsibleDoctorId?: string
				representativeDoctorId?: string
				bsnrName?: string
				bsnrStreet: string
				bsnrNumber: string
				bsnrPostCode: string
				password: string
				isParticipationActive: boolean
				types: Array<common.UserType>
				deviceId?: string
				userName: string
				externalId: string
				hpmEndpoint: string
				hzvContracts: Array<admin_bff.Contract>
				favContracts: Array<admin_bff.Contract>
				bsnrs: Array<string>
				bsnrIds?: Array<string>
				eHKSType?: common.EHKSType
				isDoctor: boolean
		}
	

		export interface GetAllInitialResponse {
				data: Array<string>
		}
	

		export interface GetEmployeeByExternalIdRequest {
				externalId: string
		}
	

		export interface UpdateDeviceRequest {
				deviceId: string
		}
	

		export interface GetEmployeeByDeviceIdRequest {
				deviceId: string
		}
	

		export interface GetByCareProviderIdRequest {
				careProviderId: string
		}
	

		export interface SearchEmployeeByNameRequest {
				name: string
				careProviderId: string
				paginationRequest: common.PaginationRequest
		}
	

		export interface SearchEmployeeByNameResponse {
				profiles: Array<EmployeeProfileResponse>
				paginationResponse: common.PaginationResponse
		}
	

		export interface UpdateEmployeeStatusRequest {
				externalId: string
				status: common.EmployeeStatus
		}
	

		export interface ResetEmployeePasswordRequest {
				externalId: string
		}
	

		export interface ResetEmployeePasswordResponse {
				password: string
		}
	

		export interface ResetEmployee2FARequest {
				externalId: string
		}
	

		export interface GetByIdRequest {
				patientId: string
		}
	

		export interface PatientProfileCreateRequest {
				patientId: string
				title: string
				lastName: string
				firstName: string
				dateOfBirth?: number
				gender: patient_profile_common.Gender
				street: string
				houseNumber: string
				postCode: string
				cityState: string
				country: string
				primaryContact: string
				secondaryContact: string
				contactPerson: string
				email: string
				intendWords: string
		}
	

		export interface PatientProfileUpdateRequest {
				patientId: string
				title: string
				firstName: string
				lastName: string
				dateOfBirth?: number
				gender: patient_profile_common.Gender
				street: string
				houseNumber: string
				postCode: string
				cityState: string
				country: string
				primaryContact: string
				secondaryContact: string
				contactPerson: string
				email: string
				intendWords: string
				additionalName: string
				job: string
				company: string
				previousDoctor: string
				additionalAddress: string
				postfach: string
				plzPostfach: string
				stadtPostfach: string
				isEmployee: IsEmploymentAnswer
				jobStatus: string
				workingHourInWeek: number
				workActivity1: WorkActivity1
				workActivity2: WorkActivity2
				employmentInformationLastUpdated: number
				specialProblemAtWork: string
		}
	

		export interface UpdatePatientMedicalDataRequest {
				patientId: string
				patientMedicalData: patient_profile_common.PatientMedicalData
		}
	

		export interface UpdatePatientMedicalDataResponse {
				medicalDataHistoryId: string
				patientMedicalData: patient_profile_common.PatientMedicalData
		}
	

		export interface PatientProfile {
				id: string
				firstName: string
				lastName: string
				dateOfBirth?: number
				hpmInformation: Array<HpmInformation>
				patientMedicalData: patient_profile_common.PatientMedicalData
				patientInfo: patient_profile_common.PatientInfo
				employmentInfoUpdatedAt?: number
				medicalDataUpdatedAt?: number
		}
	

		export interface PatientResponse {
				id: string
				accountId: string
				careProviderId: string
		}
	

		export interface HpmInformation {
				checkedDate: number
				contractId: string
				status: HpmInformationStatus
		}
	

		export interface UpdateHpmInformationRequest {
				patientId: string
				hpmInformation: Array<HpmInformation>
		}
	

		export interface GetProfileByIdsRequest {
				ids: Array<string>
		}
	

		export interface GetProfileByIdsResponse {
				patientProfiles: Array<PatientProfile>
		}
	

		export interface EventHealthInsuranceChange {
				insuranceNumber: string
				ikNumber: number
				patientId: string
		}
	


// enum definitions
    export enum WorkActivity1 {
        Physical = "Physical",
        Mental = "Mental",
    }

    export enum WorkActivity2 {
        Standing = "Standing",
        Sitting = "Sitting",
    }

    export enum IsEmploymentAnswer {
        Yes = "Yes",
        No = "No",
    }

    export enum HpmInformationStatus {
        HpmInformationStatus_Active = "Active",
        HpmInformationStatus_InActive = "InActive",
        HpmInformationStatus_Error = "Error",
    }


// method name convention const
		export const EVENT_GetAll = "api.service.domains.EmployeeProfileService.GetAll";
		export const EVENT_GetAll_Response = "api.service.domains.EmployeeProfileService.GetAll.Response";
		export const EVENT_SearchEmployeeByName = "api.service.domains.EmployeeProfileService.SearchEmployeeByName";
		export const EVENT_SearchEmployeeByName_Response = "api.service.domains.EmployeeProfileService.SearchEmployeeByName.Response";
		export const EVENT_GetEmployeeByExternalId = "api.service.domains.EmployeeProfileService.GetEmployeeByExternalId";
		export const EVENT_GetEmployeeByExternalId_Response = "api.service.domains.EmployeeProfileService.GetEmployeeByExternalId.Response";
		export const EVENT_GetAllInitial = "api.service.domains.EmployeeProfileService.GetAllInitial";
		export const EVENT_GetAllInitial_Response = "api.service.domains.EmployeeProfileService.GetAllInitial.Response";
		export const EVENT_getMyEmployeeProfile = "api.service.domains.EmployeeProfileService.GetMyEmployeeProfile";
		export const EVENT_getMyEmployeeProfile_Response = "api.service.domains.EmployeeProfileService.GetMyEmployeeProfile.Response";
		export const EVENT_getEmployeeProfileByLanrId = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByLanrId";
		export const EVENT_getEmployeeProfileByLanrId_Response = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByLanrId.Response";
		export const EVENT_getEmployeeProfilesByBsnrId = "api.service.domains.EmployeeProfileService.GetEmployeeProfilesByBsnrId";
		export const EVENT_getEmployeeProfilesByBsnrId_Response = "api.service.domains.EmployeeProfileService.GetEmployeeProfilesByBsnrId.Response";
		export const EVENT_getEmployeeProfileByHzvId = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByHzvId";
		export const EVENT_getEmployeeProfileByHzvId_Response = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByHzvId.Response";
		export const EVENT_getEmployeeProfileByMediId = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByMediId";
		export const EVENT_getEmployeeProfileByMediId_Response = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByMediId.Response";
		export const EVENT_getEmployeeProfileById = "api.service.domains.EmployeeProfileService.GetEmployeeProfileById";
		export const EVENT_getEmployeeProfileById_Response = "api.service.domains.EmployeeProfileService.GetEmployeeProfileById.Response";
		export const EVENT_getEmployeeProfileByIds = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByIds";
		export const EVENT_getEmployeeProfileByIds_Response = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByIds.Response";
		export const EVENT_createEmployeeProfile = "api.service.domains.EmployeeProfileService.CreateEmployeeProfile";
		export const EVENT_createEmployeeProfile_Response = "api.service.domains.EmployeeProfileService.CreateEmployeeProfile.Response";
		export const EVENT_CreateEmployeeProfileWithoutAuth = "api.service.domains.EmployeeProfileService.CreateEmployeeProfileWithoutAuth";
		export const EVENT_CreateEmployeeProfileWithoutAuth_Response = "api.service.domains.EmployeeProfileService.CreateEmployeeProfileWithoutAuth.Response";
		export const EVENT_updateEmployeeProfile = "api.service.domains.EmployeeProfileService.UpdateEmployeeProfile";
		export const EVENT_updateEmployeeProfile_Response = "api.service.domains.EmployeeProfileService.UpdateEmployeeProfile.Response";
		export const EVENT_updateEmployeeStatus = "api.service.domains.EmployeeProfileService.UpdateEmployeeStatus";
		export const EVENT_updateEmployeeStatus_Response = "api.service.domains.EmployeeProfileService.UpdateEmployeeStatus.Response";
		export const EVENT_deleteEmployeeProfileById = "api.service.domains.EmployeeProfileService.DeleteEmployeeProfileById";
		export const EVENT_deleteEmployeeProfileById_Response = "api.service.domains.EmployeeProfileService.DeleteEmployeeProfileById.Response";
		export const EVENT_GetEmployeeByIds = "api.service.domains.EmployeeProfileService.GetEmployeeByIds";
		export const EVENT_GetEmployeeByIds_Response = "api.service.domains.EmployeeProfileService.GetEmployeeByIds.Response";
		export const EVENT_UpdateDevice = "api.service.domains.EmployeeProfileService.UpdateDevice";
		export const EVENT_UpdateDevice_Response = "api.service.domains.EmployeeProfileService.UpdateDevice.Response";
		export const EVENT_GetEmployeeByDeviceId = "api.service.domains.EmployeeProfileService.GetEmployeeByDeviceId";
		export const EVENT_GetEmployeeByDeviceId_Response = "api.service.domains.EmployeeProfileService.GetEmployeeByDeviceId.Response";
		export const EVENT_ResetEmployeePassword = "api.service.domains.EmployeeProfileService.ResetEmployeePassword";
		export const EVENT_ResetEmployeePassword_Response = "api.service.domains.EmployeeProfileService.ResetEmployeePassword.Response";
		export const EVENT_ResetEmployee2FA = "api.service.domains.EmployeeProfileService.ResetEmployee2FA";
		export const EVENT_ResetEmployee2FA_Response = "api.service.domains.EmployeeProfileService.ResetEmployee2FA.Response";
		export const EVENT_UpdateDefaultBsnrOfEmployee = "api.service.domains.EmployeeProfileService.UpdateDefaultBsnrOfEmployee";
		export const EVENT_UpdateDefaultBsnrOfEmployee_Response = "api.service.domains.EmployeeProfileService.UpdateDefaultBsnrOfEmployee.Response";
		export const EVENT_HandleEventDeviceChange = "api.service.domains.EmployeeProfileService.HandleEventDeviceChange";
		export const EVENT_HandleEventDeviceChange_Response = "api.service.domains.EmployeeProfileService.HandleEventDeviceChange.Response";

		export const EVENT_CreateProfile = "api.service.domains.PatientProfileService.CreateProfile";
		export const EVENT_CreateProfile_Response = "api.service.domains.PatientProfileService.CreateProfile.Response";
		export const EVENT_UpdateProfile = "api.service.domains.PatientProfileService.UpdateProfile";
		export const EVENT_UpdateProfile_Response = "api.service.domains.PatientProfileService.UpdateProfile.Response";
		export const EVENT_CreatePatientMedicalData = "api.service.domains.PatientProfileService.CreatePatientMedicalData";
		export const EVENT_CreatePatientMedicalData_Response = "api.service.domains.PatientProfileService.CreatePatientMedicalData.Response";
		export const EVENT_GetProfileById = "api.service.domains.PatientProfileService.GetProfileById";
		export const EVENT_GetProfileById_Response = "api.service.domains.PatientProfileService.GetProfileById.Response";
		export const EVENT_GetProfileByIds = "api.service.domains.PatientProfileService.GetProfileByIds";
		export const EVENT_GetProfileByIds_Response = "api.service.domains.PatientProfileService.GetProfileByIds.Response";
		export const EVENT_UpdateHpmInformation = "api.service.domains.PatientProfileService.UpdateHpmInformation";
		export const EVENT_UpdateHpmInformation_Response = "api.service.domains.PatientProfileService.UpdateHpmInformation.Response";


// Define constants
// method name convention const
        export const LEGACY_TOPIC_GetAll = "/api/service/domains/employee/profile/getAll";
        export const LEGACY_TOPIC_SearchEmployeeByName = "/api/service/domains/employee/profile/searchEmployeeByName";
        export const LEGACY_TOPIC_GetEmployeeByExternalId = "/api/service/domains/employee/profile/getEmployeeByExternalId";
        export const LEGACY_TOPIC_GetAllInitial = "/api/service/domains/employee/profile/getAllInitial";
        export const LEGACY_TOPIC_GetMyEmployeeProfile = "/api/service/domains/employee/profile/getMyEmployeeProfile";
        export const LEGACY_TOPIC_GetEmployeeProfileByLanrId = "/api/service/domains/employee/profile/getEmployeeProfileByLanrId";
        export const LEGACY_TOPIC_GetEmployeeProfilesByBsnrId = "/api/service/domains/employee/profile/getEmployeeProfilesByBsnrId";
        export const LEGACY_TOPIC_GetEmployeeProfileByHzvId = "/api/service/domains/employee/profile/getEmployeeProfileByHzvId";
        export const LEGACY_TOPIC_GetEmployeeProfileByMediId = "/api/service/domains/employee/profile/getEmployeeProfileByMediId";
        export const LEGACY_TOPIC_GetEmployeeProfileById = "/api/service/domains/employee/profile/getEmployeeProfileById";
        export const LEGACY_TOPIC_GetEmployeeProfileByIds = "/api/service/domains/employee/profile/getEmployeeProfileByIds";
        export const LEGACY_TOPIC_CreateEmployeeProfile = "/api/service/domains/employee/profile/createEmployeeProfile";
        export const LEGACY_TOPIC_CreateEmployeeProfileWithoutAuth = "/api/service/domains/employee/profile/createEmployeeProfileWithoutAuth";
        export const LEGACY_TOPIC_UpdateEmployeeProfile = "/api/service/domains/employee/profile/updateEmployeeProfile";
        export const LEGACY_TOPIC_UpdateEmployeeStatus = "/api/service/domains/employee/profile/updateEmployeeStatus";
        export const LEGACY_TOPIC_DeleteEmployeeProfileById = "/api/service/domains/employee/profile/deleteEmployeeProfileById";
        export const LEGACY_TOPIC_GetEmployeeByIds = "/api/service/domains/employee/profile/getEmployeeByIds";
        export const LEGACY_TOPIC_UpdateDevice = "/api/service/domains/employee/profile/updateDevice";
        export const LEGACY_TOPIC_GetEmployeeByDeviceId = "/api/service/domains/employee/profile/getEmployeeByDeviceId";
        export const LEGACY_TOPIC_ResetEmployeePassword = "/api/service/domains/employee/profile/resetEmployeePassword";
        export const LEGACY_TOPIC_ResetEmployee2FA = "/api/service/domains/employee/profile/resetEmployee2FA";
        export const LEGACY_TOPIC_UpdateDefaultBsnrOfEmployee = "/api/service/domains/employee/profile/updateDefaultBsnrOfEmployee";
        export const LEGACY_TOPIC_HandleEventDeviceChange = "/api/service/domains/employee/profile/handleEventDeviceChange";

        export const LEGACY_TOPIC_CreateProfile = "/api/service/domains/patient/profile/createProfile";
        export const LEGACY_TOPIC_UpdateProfile = "/api/service/domains/patient/profile/updateProfile";
        export const LEGACY_TOPIC_CreatePatientMedicalData = "/api/service/domains/patient/profile/createPatientMedicalData";
        export const LEGACY_TOPIC_GetProfileById = "/api/service/domains/patient/profile/getProfileById";
        export const LEGACY_TOPIC_GetProfileByIds = "/api/service/domains/patient/profile/getProfileByIds";
        export const LEGACY_TOPIC_UpdateHpmInformation = "/api/service/domains/patient/profile/updateHpmInformation";

